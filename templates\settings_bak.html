{% extends "base.html" %}

{% block title %}Settings - Kiosk Management System{% endblock %}

{% block page_header %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          Settings
        </h2>
        <div class="text-muted mt-1">Configure your kiosk system preferences</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <button class="btn btn-primary" onclick="saveSettings()">
            <i class="ti ti-device-floppy icon"></i>
            Save Changes
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block content %}
<div class="row row-deck row-cards">
  <!-- General Settings -->
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">General Settings</h3>
      </div>
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-6">
            <label class="form-label">Event Name</label>
            <input type="text" class="form-control" value="International Trade Expo 2025" placeholder="Enter event name">
          </div>
          <div class="col-md-6">
            <label class="form-label">Event Location</label>
            <input type="text" class="form-control" value="Convention Center Hall A" placeholder="Enter location">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label class="form-label">Event Start Date</label>
            <input type="date" class="form-control" value="2025-09-30">
          </div>
          <div class="col-md-6">
            <label class="form-label">Event End Date</label>
            <input type="date" class="form-control" value="2025-10-02">
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label class="form-label">Welcome Message</label>
            <textarea class="form-control" rows="3" placeholder="Enter welcome message">Welcome to International Trade Expo 2025! Please scan your QR code to print your badge.</textarea>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Kiosk Display Settings -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Kiosk Display Settings</h3>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label class="form-label">Display Orientation</label>
          <select class="form-select">
            <option value="horizontal" selected>Horizontal (Landscape)</option>
            <option value="vertical">Vertical (Portrait)</option>
            <option value="auto">Auto-detect</option>
          </select>
        </div>
        <div class="mb-3">
          <label class="form-label">Idle Timeout (seconds)</label>
          <input type="number" class="form-control" value="30" placeholder="30">
          <small class="form-hint">Time before returning to idle screen</small>
        </div>
        <div class="mb-3">
          <label class="form-label">Ad Rotation Interval (seconds)</label>
          <input type="number" class="form-control" value="10" placeholder="10">
          <small class="form-hint">Time between ad transitions</small>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Enable Screensaver</span>
          </label>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Show Scan Counter</span>
          </label>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Play Sound on Scan</span>
          </label>
        </div>
      </div>
    </div>
  </div>

  <!-- Printer Settings -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Printer Settings</h3>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label class="form-label">Printer Name</label>
          <select class="form-select">
            <option value="default" selected>Default Printer</option>
            <option value="zebra_zd420">Zebra ZD420</option>
            <option value="dymo_450">DYMO LabelWriter 450</option>
            <option value="brother_ql">Brother QL-820NWB</option>
          </select>
        </div>
        <div class="mb-3">
          <label class="form-label">Badge Size</label>
          <select class="form-select">
            <option value="4x6" selected>4" x 6" (Standard)</option>
            <option value="3x4">3" x 4" (Small)</option>
            <option value="4x3">4" x 3" (Landscape)</option>
          </select>
        </div>
        <div class="mb-3">
          <label class="form-label">Print Quality</label>
          <select class="form-select">
            <option value="draft">Draft (Fast)</option>
            <option value="normal" selected>Normal</option>
            <option value="high">High Quality</option>
          </select>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Auto-print on Scan</span>
          </label>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox">
            <span class="form-check-label">Print Duplicate Copy</span>
          </label>
        </div>
        <div class="mt-3">
          <button class="btn btn-outline-primary w-100">
            <i class="ti ti-printer icon me-2"></i>
            Test Print
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Scanner Settings -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Scanner Settings</h3>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label class="form-label">Scanner Type</label>
          <select class="form-select">
            <option value="usb" selected>USB Scanner</option>
            <option value="camera">Camera Scanner</option>
            <option value="bluetooth">Bluetooth Scanner</option>
          </select>
        </div>
        <div class="mb-3">
          <label class="form-label">Scan Timeout (seconds)</label>
          <input type="number" class="form-control" value="5" placeholder="5">
          <small class="form-hint">Maximum time to wait for scan</small>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Enable Beep on Scan</span>
          </label>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Validate QR Code Format</span>
          </label>
        </div>
        <div class="mt-3">
          <button class="btn btn-outline-primary w-100">
            <i class="ti ti-qrcode icon me-2"></i>
            Test Scanner
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Network Settings -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Network & API Settings</h3>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label class="form-label">API Endpoint</label>
          <input type="url" class="form-control" value="http://localhost:5000/api" placeholder="Enter API URL">
        </div>
        <div class="mb-3">
          <label class="form-label">API Key</label>
          <input type="password" class="form-control" value="••••••••••••" placeholder="Enter API key">
        </div>
        <div class="mb-3">
          <label class="form-label">Connection Timeout (seconds)</label>
          <input type="number" class="form-control" value="10" placeholder="10">
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Enable SSL/TLS</span>
          </label>
        </div>
        <div class="mt-3">
          <button class="btn btn-outline-primary w-100">
            <i class="ti ti-plug icon me-2"></i>
            Test Connection
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Advanced Settings -->
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Advanced Settings</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-check form-switch">
                <input class="form-check-input" type="checkbox" checked>
                <span class="form-check-label">Enable Logging</span>
              </label>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-check form-switch">
                <input class="form-check-input" type="checkbox">
                <span class="form-check-label">Debug Mode</span>
              </label>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-check form-switch">
                <input class="form-check-input" type="checkbox" checked>
                <span class="form-check-label">Auto-update</span>
              </label>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-md-12">
            <div class="alert alert-info">
              <i class="ti ti-info-circle icon me-2"></i>
              <strong>Note:</strong> Changes to advanced settings may require system restart.
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <div class="d-flex">
          <button class="btn btn-link">Reset to Defaults</button>
          <button class="btn btn-primary ms-auto" onclick="saveSettings()">Save All Settings</button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  function saveSettings() {
    // Show success message
    alert('Settings saved successfully!');
  }
</script>
{% endblock %}

