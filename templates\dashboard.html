{% extends "base.html" %}

{% block title %}Dashboard - Kiosk Management System{% endblock %}

{% block page_header %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          Dashboard
        </h2>
        <div class="text-muted mt-1">Monitor your kiosk system performance and activity</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <button class="btn btn-primary d-none d-sm-inline-block" onclick="refreshDashboard()">
            <i class="ti ti-refresh icon"></i>
            Refresh
          </button>
          <button class="btn btn-primary d-sm-none btn-icon" onclick="refreshDashboard()">
            <i class="ti ti-refresh icon"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block content %}
<div class="row row-deck row-cards">
  <!-- Statistics Cards -->
  <div class="col-sm-6 col-lg-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="subheader">Total Scans</div>
          <div class="ms-auto lh-1">
            <div class="dropdown">
              <a class="dropdown-toggle text-muted" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Last 7 days</a>
              <div class="dropdown-menu dropdown-menu-end">
                <a class="dropdown-item active" href="#">Last 7 days</a>
                <a class="dropdown-item" href="#">Last 30 days</a>
                <a class="dropdown-item" href="#">Last 3 months</a>
              </div>
            </div>
          </div>
        </div>
        <div class="d-flex align-items-baseline">
          <div class="h1 mb-0 me-2">{{ stats.total_scans }}</div>
          <div class="me-auto">
            <span class="text-green d-inline-flex align-items-center lh-1">
              8% <i class="ti ti-trending-up icon ms-1"></i>
            </span>
          </div>
        </div>
      </div>
      <div id="chart-scans" class="chart-sm"></div>
    </div>
  </div>

  <div class="col-sm-6 col-lg-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="subheader">Today's Scans</div>
          <div class="ms-auto lh-1">
            <span class="badge bg-green-lt">Live</span>
          </div>
        </div>
        <div class="d-flex align-items-baseline">
          <div class="h1 mb-0 me-2">{{ stats.today_scans }}</div>
          <div class="me-auto">
            <span class="text-green d-inline-flex align-items-center lh-1">
              12% <i class="ti ti-trending-up icon ms-1"></i>
            </span>
          </div>
        </div>
      </div>
      <div id="chart-today" class="chart-sm"></div>
    </div>
  </div>

  <div class="col-sm-6 col-lg-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="subheader">Badges Printed</div>
        </div>
        <div class="d-flex align-items-baseline">
          <div class="h1 mb-0 me-2">{{ stats.total_badges }}</div>
          <div class="me-auto">
            <span class="text-green d-inline-flex align-items-center lh-1">
              96% <i class="ti ti-check icon ms-1"></i>
            </span>
          </div>
        </div>
      </div>
      <div id="chart-badges" class="chart-sm"></div>
    </div>
  </div>

  <div class="col-sm-6 col-lg-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="subheader">Active Ads</div>
        </div>
        <div class="d-flex align-items-baseline">
          <div class="h1 mb-0 me-2">{{ stats.active_ads }}</div>
          <div class="me-auto">
            <span class="badge bg-blue-lt">Running</span>
          </div>
        </div>
      </div>
      <div id="chart-ads" class="chart-sm"></div>
    </div>
  </div>

  <!-- System Status -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">System Status</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-6">
            <div class="mb-3">
              <div class="d-flex align-items-center mb-2">
                <div class="me-2">
                  <i class="ti ti-printer icon text-blue"></i>
                </div>
                <div>
                  <strong>Printer</strong>
                </div>
                <div class="ms-auto">
                  <span class="status status-green">
                    <span class="status-dot"></span>
                    {{ stats.printer_status }}
                  </span>
                </div>
              </div>
              <div class="progress progress-sm">
                <div class="progress-bar bg-blue" style="width: 85%" role="progressbar">
                  <span class="visually-hidden">85%</span>
                </div>
              </div>
              <div class="text-muted small mt-1">Paper: 85%</div>
            </div>
          </div>
          <div class="col-6">
            <div class="mb-3">
              <div class="d-flex align-items-center mb-2">
                <div class="me-2">
                  <i class="ti ti-qrcode icon text-green"></i>
                </div>
                <div>
                  <strong>Scanner</strong>
                </div>
                <div class="ms-auto">
                  <span id="scanner-status-badge" class="status status-gray">
                    <span class="status-dot"></span>
                    Checking...
                  </span>
                </div>
              </div>
              <div class="progress progress-sm">
                <div id="scanner-progress" class="progress-bar bg-gray" style="width: 0%" role="progressbar">
                  <span class="visually-hidden">0%</span>
                </div>
              </div>
              <div class="text-muted small mt-1">
                <span id="scanner-status-text">Initializing...</span>
                <a href="{{ url_for('scanner_status_page') }}" class="ms-2 text-primary">View Details</a>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-6">
            <div class="mb-3">
              <div class="d-flex align-items-center mb-2">
                <div class="me-2">
                  <i class="ti ti-database icon text-purple"></i>
                </div>
                <div>
                  <strong>Database</strong>
                </div>
                <div class="ms-auto">
                  <span class="status status-green">
                    <span class="status-dot"></span>
                    Connected
                  </span>
                </div>
              </div>
              <div class="text-muted small">Response: 12ms</div>
            </div>
          </div>
          <div class="col-6">
            <div class="mb-3">
              <div class="d-flex align-items-center mb-2">
                <div class="me-2">
                  <i class="ti ti-wifi icon text-orange"></i>
                </div>
                <div>
                  <strong>Network</strong>
                </div>
                <div class="ms-auto">
                  <span class="status status-green">
                    <span class="status-dot"></span>
                    Online
                  </span>
                </div>
              </div>
              <div class="text-muted small">Latency: 8ms</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Activity Chart -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Scan Activity (Last 7 Days)</h3>
      </div>
      <div class="card-body">
        <div id="chart-activity"></div>
      </div>
    </div>
  </div>

  <!-- Recent Scans -->
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Recent Scans</h3>
        <div class="ms-auto">
          <span class="badge bg-green-lt">Live Updates</span>
        </div>
      </div>
      <div class="table-responsive">
        <table class="table table-vcenter card-table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Participant</th>
              <th>Company</th>
              <th>Time</th>
              <th>Status</th>
              <th class="w-1"></th>
            </tr>
          </thead>
          <tbody>
            {% for scan in recent_scans %}
            <tr>
              <td class="text-muted">#{{ scan.id }}</td>
              <td>
                <div class="d-flex py-1 align-items-center">
                  <span class="avatar me-2" style="background-image: url(https://ui-avatars.com/api/?name={{ scan.name }}&background=random)"></span>
                  <div class="flex-fill">
                    <div class="font-weight-medium">{{ scan.name }}</div>
                  </div>
                </div>
              </td>
              <td>
                <div class="text-muted">{{ scan.company }}</div>
              </td>
              <td class="text-muted">{{ scan.time }}</td>
              <td>
                <span class="badge bg-success-lt">{{ scan.status }}</span>
              </td>
              <td>
                <div class="btn-list flex-nowrap">
                  <button class="btn btn-sm btn-ghost-secondary">
                    <i class="ti ti-eye icon"></i>
                  </button>
                  <button class="btn btn-sm btn-ghost-secondary">
                    <i class="ti ti-printer icon"></i>
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      <div class="card-footer d-flex align-items-center">
        <p class="m-0 text-muted">Showing <span>5</span> of <span>{{ stats.total_scans }}</span> entries</p>
        <ul class="pagination m-0 ms-auto">
          <li class="page-item disabled">
            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">
              <i class="ti ti-chevron-left icon"></i>
              prev
            </a>
          </li>
          <li class="page-item active"><a class="page-link" href="#">1</a></li>
          <li class="page-item"><a class="page-link" href="#">2</a></li>
          <li class="page-item"><a class="page-link" href="#">3</a></li>
          <li class="page-item">
            <a class="page-link" href="#">
              next
              <i class="ti ti-chevron-right icon"></i>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  let eventSource = null;
  let lastScannerStatus = null;

  // Initialize real-time updates for dashboard
  function initializeDashboardEvents() {
    if (eventSource) {
      eventSource.close();
    }

    eventSource = new EventSource('/api/events');

    eventSource.onmessage = function(event) {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'scanner_status') {
          updateScannerStatusDisplay(data.data.status);
        }
      } catch (error) {
        console.error('Error parsing SSE data:', error);
      }
    };

    eventSource.onerror = function(error) {
      console.error('Dashboard SSE connection error:', error);
      // Fallback to polling if SSE fails
      setTimeout(updateScannerStatus, 2000);
    };
  }

  // Update scanner status display with caching
  function updateScannerStatusDisplay(status) {
    // Only update if status actually changed
    if (lastScannerStatus === status) {
      return;
    }
    lastScannerStatus = status;

    const badge = document.getElementById('scanner-status-badge');
    const progress = document.getElementById('scanner-progress');
    const text = document.getElementById('scanner-status-text');

    // Remove all status classes
    badge.className = 'status';
    progress.className = 'progress-bar';

    switch(status) {
      case 'Ready':
        badge.classList.add('status-green');
        progress.classList.add('bg-green');
        progress.style.width = '100%';
        text.textContent = 'Ready to scan';
        break;
      case 'Connected':
        badge.classList.add('status-blue');
        progress.classList.add('bg-blue');
        progress.style.width = '75%';
        text.textContent = 'Connected';
        break;
      case 'Scanning':
        badge.classList.add('status-yellow');
        progress.classList.add('bg-yellow');
        progress.style.width = '90%';
        text.textContent = 'Scanning...';
        break;
      case 'Disconnected':
        badge.classList.add('status-gray');
        progress.classList.add('bg-gray');
        progress.style.width = '0%';
        text.textContent = 'Disconnected';
        break;
      case 'Not Configured':
        badge.classList.add('status-orange');
        progress.classList.add('bg-orange');
        progress.style.width = '25%';
        text.textContent = 'Not configured';
        break;
      case 'Error':
      default:
        badge.classList.add('status-red');
        progress.classList.add('bg-red');
        progress.style.width = '10%';
        text.textContent = 'Error';
        break;
    }

    // Update badge text
    const statusDot = badge.querySelector('.status-dot');
    badge.innerHTML = '';
    badge.appendChild(statusDot);
    badge.appendChild(document.createTextNode(status));
  }

  // Fallback polling function (only used if SSE fails)
  function updateScannerStatus() {
    fetch('/api/scanner/status')
      .then(response => response.json())
      .then(data => {
        updateScannerStatusDisplay(data.status);
      })
      .catch(error => {
        console.error('Error fetching scanner status:', error);
        updateScannerStatusDisplay('Error');
      });
  }

  // Refresh dashboard data
  function refreshDashboard() {
    location.reload();
  }

  // Initialize dashboard
  document.addEventListener('DOMContentLoaded', function() {
    // Try real-time updates first
    if (typeof(EventSource) !== "undefined") {
      initializeDashboardEvents();
    } else {
      // Fallback to polling for older browsers
      updateScannerStatus();
      setInterval(updateScannerStatus, 15000); // Less frequent polling
    }
  });

  // Clean up when page is unloaded
  window.addEventListener('beforeunload', function() {
    if (eventSource) {
      eventSource.close();
    }
  });

  // Auto-refresh entire dashboard much less frequently
  setInterval(refreshDashboard, 300000); // Every 5 minutes instead of 30 seconds
</script>
{% endblock %}