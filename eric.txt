
pip install watchdog



from flask import Flask, render_template, request, redirect, url_for, jsonify, flash
from flask_sqlalchemy import SQLAlchemy

app = Flask(__name__)

app.config['SQLALCHEMY_DATABASE_URI'] = (
    f"mysql+pymysql://{os.getenv('MYSQL_USER')}:{os.getenv('MYSQL_PASSWORD')}"
    f"@{os.getenv('MYSQL_HOST')}:{os.getenv('MYSQL_PORT')}/{os.getenv('MYSQL_DB')}"
)

app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

class Kiosk(db.Model):
    __tablename__ = 'kiosk_setting'
    id = db.Column(db.Integer, primary_key=True)
    kiosk_id = db.Column(db.Integer)
    scanner_com_port = db.Column(db.String(10))

@app.route('/update_settings', methods=['POST'])
def update_settings():
    """Update kiosk settings"""
    config = load_config()
    scanner_com_port = request.form.get('scanner_com_port')
    # 1. Validate input exists
    if not scanner_com_port:
        return jsonify({'error': 'Missing scanner_com_port'}), 400

    # 2. Find the kiosk record
    kiosk = Kiosk.query.filter_by(kiosk_id=KIOSK_ID).first()
    if not kiosk:
        return jsonify({'error': 'Kiosk not found'}), 404

    # 3. Try to update and commit
    try:
        kiosk.scanner_com_port = scanner_com_port
        db.session.commit()
        return jsonify({'message': 'Settings updated successfully'}), 200

    except SQLAlchemyError as e:
        db.session.rollback()
        return jsonify({'error': 'Database error', 'details': str(e)}), 500