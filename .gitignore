# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
kiosk1/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Flask
instance/
.webassets-cache

# Environment variables
.env
.env.local
.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Uploads
static/uploads/ads/*
!static/uploads/ads/.gitkeep

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# OS
Thumbs.db
.DS_Store

# Testing
.pytest_cache/
.coverage
htmlcov/

# Production
*.pid
*.sock

