<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>Kiosk - {{ expo_info.name }}</title>
    
    <!-- CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta20/dist/css/tabler.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/tabler-icons.min.css" rel="stylesheet"/>
    
    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
        --tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe <PERSON>I, Roboto, Helvetica Neue, sans-serif;
        --primary-color: #667eea;
        --secondary-color: #764ba2;
        --success-color: #2fb344;
        --warning-color: #f59e0b;
        --error-color: #ef4444;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --bg-glass: rgba(255, 255, 255, 0.95);
      }

      body {
        font-feature-settings: "cv03", "cv04", "cv11";
        margin: 0;
        padding: 0;
        overflow: hidden;
        background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        font-family: var(--tblr-font-sans-serif);
      }

      .kiosk-container {
        display: flex;
        flex-direction: column;
        height: 100vh;
        width: 100vw;
        position: relative;
      }

      .header-section {
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        padding: 2rem 1.5rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 10;
      }

      .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 1200px;
        margin: 0 auto;
      }

      .logo-section {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .logo-container {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
      }

      .expo-info h1 {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0 0 0.25rem 0;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .expo-info p {
        font-size: 1rem;
        color: var(--text-secondary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .status-section {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .ads-section {
        height: 35vh;
        background: #000;
        position: relative;
        overflow: hidden;
        border-radius: 0;
      }

      .scan-section {
        flex: 1;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 3rem 2rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        min-height: 400px;
      }

      .scanner-container {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
      }

      .qr-scanner {
        width: 280px;
        height: 280px;
        border: 4px solid transparent;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(135deg, var(--primary-color), var(--secondary-color)) border-box;
        box-shadow: 0 20px 60px rgba(102, 126, 234, 0.2);
        transition: all 0.3s ease;
      }

      .qr-scanner.scanning {
        animation: scannerPulse 2s infinite;
        box-shadow: 0 20px 60px rgba(102, 126, 234, 0.4);
      }

      .qr-scanner.success {
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(135deg, var(--success-color), #10b981) border-box;
        animation: successPulse 0.6s ease;
      }

      @keyframes scannerPulse {
        0%, 100% {
          transform: scale(1);
          box-shadow: 0 20px 60px rgba(102, 126, 234, 0.2);
        }
        50% {
          transform: scale(1.02);
          box-shadow: 0 25px 80px rgba(102, 126, 234, 0.4);
        }
      }

      @keyframes successPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }
      
      .scanner-icon {
        font-size: 120px;
        color: var(--primary-color);
        margin-bottom: 1rem;
        transition: all 0.3s ease;
      }

      .scanner-icon.scanning {
        animation: iconScan 2s infinite;
      }

      @keyframes iconScan {
        0%, 100% {
          color: var(--primary-color);
          transform: scale(1);
        }
        50% {
          color: var(--secondary-color);
          transform: scale(1.1);
        }
      }

      .welcome-text {
        text-align: center;
        margin-bottom: 2rem;
      }

      .welcome-text h2 {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0 0 0.5rem 0;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .welcome-text p {
        font-size: 1.25rem;
        color: var(--text-secondary);
        margin: 0;
        font-weight: 500;
      }

      .scan-instruction {
        background: var(--bg-glass);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1rem;
        color: var(--text-secondary);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        margin-top: 2rem;
      }

      .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        background: var(--bg-glass);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      .status-ready {
        color: var(--success-color);
      }

      .status-scanning {
        color: var(--warning-color);
      }

      .status-error {
        color: var(--error-color);
      }

      .pulse-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        animation: pulse-dot 2s infinite;
      }

      .pulse-dot.ready {
        background: var(--success-color);
      }

      .pulse-dot.scanning {
        background: var(--warning-color);
      }

      .pulse-dot.error {
        background: var(--error-color);
      }

      @keyframes pulse-dot {
        0%, 100% {
          opacity: 1;
          transform: scale(1);
        }
        50% {
          opacity: 0.5;
          transform: scale(1.2);
        }
      }

      .success-animation {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        width: 100%;
        z-index: 20;
      }

      .success-animation.show {
        display: block;
        animation: successShow 3s ease-in-out;
      }

      @keyframes successShow {
        0% {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.8);
        }
        20% {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1.1);
        }
        80% {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
        100% {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.9);
        }
      }

      .success-icon {
        font-size: 120px;
        color: var(--success-color);
        margin-bottom: 1rem;
        animation: successBounce 0.6s ease;
      }

      @keyframes successBounce {
        0%, 20%, 50%, 80%, 100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-20px);
        }
        60% {
          transform: translateY(-10px);
        }
      }

      .success-text h2 {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--success-color);
        margin: 0 0 0.5rem 0;
      }

      .success-text p {
        font-size: 1.25rem;
        color: var(--text-secondary);
        margin: 0;
      }

      .ad-container {
        width: 100%;
        height: 100%;
        position: relative;
        border-radius: 0;
        overflow: hidden;
      }

      .ad-slide {
        display: none;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: opacity 0.5s ease;
      }

      .ad-slide.active {
        display: block;
        opacity: 1;
      }

      .ad-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        height: 100%;
        color: white;
        text-align: center;
        padding: 2rem;
      }

      .ad-placeholder i {
        font-size: 80px;
        margin-bottom: 1rem;
        opacity: 0.8;
      }

      .ad-placeholder h3 {
        font-size: 1.75rem;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
      }

      .ad-placeholder p {
        font-size: 1rem;
        opacity: 0.9;
        margin: 0;
      }

      .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
      }

      .floating-element {
        position: absolute;
        opacity: 0.1;
        animation: float 20s infinite linear;
      }

      @keyframes float {
        0% {
          transform: translateY(100vh) rotate(0deg);
        }
        100% {
          transform: translateY(-100px) rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div class="kiosk-container">
      <!-- Floating Background Elements -->
      <div class="floating-elements">
        <i class="ti ti-qrcode floating-element" style="top: 10%; left: 10%; font-size: 60px; animation-delay: 0s;"></i>
        <i class="ti ti-scan floating-element" style="top: 20%; right: 15%; font-size: 40px; animation-delay: 5s;"></i>
        <i class="ti ti-printer floating-element" style="top: 60%; left: 5%; font-size: 50px; animation-delay: 10s;"></i>
        <i class="ti ti-badge floating-element" style="top: 70%; right: 10%; font-size: 45px; animation-delay: 15s;"></i>
      </div>

      <!-- Header -->
      <div class="header-section">
        <div class="header-content">
          <div class="logo-section">
            <div class="logo-container">
              <i class="ti ti-qrcode" style="font-size: 32px; color: white;"></i>
            </div>
            <div class="expo-info">
              <h1>{{ expo_info.name }}</h1>
              <p>
                <i class="ti ti-calendar"></i>
                {{ expo_info.date }}
              </p>
            </div>
          </div>
          <div class="status-section">
            <div class="status-indicator" id="scanner-status">
              <span class="pulse-dot ready" id="status-dot"></span>
              <span id="status-text">Ready</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Ads Section -->
      <div class="ads-section">
        <div class="ad-container" id="ad-container">
          <!-- Default placeholder if no ads -->
          <div class="ad-slide active">
            <div class="ad-placeholder">
              <div>
                <i class="ti ti-ad"></i>
                <h3>Your Advertisement Here</h3>
                <p>Upload promotional content in the management panel</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Scan Section -->
      <div class="scan-section">
        <div id="idle-state" class="scanner-container">
          <div class="welcome-text">
            <h2>Welcome!</h2>
            <p>Scan your QR code to print your badge</p>
          </div>

          <div class="qr-scanner" id="qr-scanner">
            <div class="text-center">
              <i class="ti ti-scan scanner-icon" id="scanner-icon"></i>
            </div>
          </div>

          <div class="scan-instruction">
            <i class="ti ti-info-circle"></i>
            <span>Position your QR code in front of the scanner</span>
          </div>
        </div>

        <div class="success-animation" id="success-state">
          <i class="ti ti-circle-check success-icon"></i>
          <div class="success-text">
            <h2>Success!</h2>
            <p>Your badge is printing...</p>
          </div>
        </div>
      </div>
      
      <!-- Footer -->
      <!-- <div class="footer-section">
        <div class="info-grid">
          <div class="info-item">
            <i class="ti ti-printer icon text-blue"></i>
            <span class="text-muted">Printer:</span>
            <span class="status-indicator status-online">
              <span class="pulse-dot"></span>
              Online
            </span>
          </div>
          <div class="info-item">
            <i class="ti ti-qrcode icon text-green"></i>
            <span class="text-muted">Scanner:</span>
            <span class="status-indicator status-online">
              <span class="pulse-dot"></span>
              Ready
            </span>
          </div>
          <div class="info-item">
            <i class="ti ti-map-pin icon text-purple"></i>
            <span class="text-muted">{{ expo_info.location }}</span>
          </div>
          <div class="info-item">
            <i class="ti ti-building icon text-orange"></i>
            <span class="text-muted">{{ expo_info.booth_count }}+ Exhibitors</span>
          </div>
        </div>
      </div> -->
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta20/dist/js/tabler.min.js"></script>
    <script>
      // Global variables
      let currentAdIndex = 0;
      let ads = [];
      let eventSource = null;
      let isScanning = false;
      let scanTimeout = null;

      // Initialize real-time scanner connection
      function initializeScanner() {
        if (eventSource) {
          eventSource.close();
        }

        eventSource = new EventSource('/api/events');

        eventSource.onmessage = function(event) {
          try {
            const data = JSON.parse(event.data);
            handleScannerEvent(data);
          } catch (error) {
            console.error('Error parsing scanner event:', error);
          }
        };

        eventSource.onerror = function(error) {
          console.error('Scanner connection error:', error);
          updateScannerStatus('error', 'Connection Error');

          // Reconnect after 5 seconds
          setTimeout(() => {
            if (eventSource.readyState === EventSource.CLOSED) {
              initializeScanner();
            }
          }, 5000);
        };

        eventSource.onopen = function() {
          console.log('Scanner connection established');
        };
      }

      // Handle real-time scanner events
      function handleScannerEvent(event) {
        switch(event.type) {
          case 'scanner_status':
            updateScannerStatus(event.data.status.toLowerCase(), event.data.status);
            break;

          case 'new_scan':
            handleNewScan(event.data.barcode);
            break;

          case 'heartbeat':
            // Keep connection alive
            break;
        }
      }

      // Update scanner status display
      function updateScannerStatus(status, text) {
        const statusIndicator = document.getElementById('scanner-status');
        const statusDot = document.getElementById('status-dot');
        const statusText = document.getElementById('status-text');
        const scanner = document.getElementById('qr-scanner');
        const scannerIcon = document.getElementById('scanner-icon');

        // Remove all status classes
        statusDot.className = 'pulse-dot';
        scanner.className = 'qr-scanner';
        scannerIcon.className = 'ti ti-scan scanner-icon';

        switch(status) {
          case 'ready':
            statusDot.classList.add('ready');
            statusText.textContent = 'Ready';
            scanner.classList.add('scanning');
            scannerIcon.classList.add('scanning');
            break;
          case 'scanning':
            statusDot.classList.add('scanning');
            statusText.textContent = 'Scanning';
            scanner.classList.add('scanning');
            scannerIcon.classList.add('scanning');
            break;
          case 'connected':
            statusDot.classList.add('ready');
            statusText.textContent = 'Connected';
            break;
          case 'error':
          case 'disconnected':
            statusDot.classList.add('error');
            statusText.textContent = text || 'Error';
            break;
          default:
            statusDot.classList.add('ready');
            statusText.textContent = text || 'Ready';
        }
      }

      // Handle new scan
      function handleNewScan(scanData) {
        if (isScanning) return; // Prevent multiple scans

        isScanning = true;
        showSuccessAnimation(scanData);

        // Reset after animation
        setTimeout(() => {
          isScanning = false;
          updateScannerStatus('ready', 'Ready');
        }, 3000);
      }

      // Show success animation
      function showSuccessAnimation(scanData) {
        const idleState = document.getElementById('idle-state');
        const successState = document.getElementById('success-state');
        const scanner = document.getElementById('qr-scanner');

        // Update scanner to success state
        scanner.classList.remove('scanning');
        scanner.classList.add('success');

        // Show success animation
        idleState.style.display = 'none';
        successState.classList.add('show');

        // Hide after 3 seconds
        setTimeout(() => {
          successState.classList.remove('show');
          idleState.style.display = 'flex';
          scanner.classList.remove('success');
        }, 3000);
      }

      // Load and rotate ads
      async function loadAds() {
        try {
          const response = await fetch('/api/ads');
          ads = await response.json();

          if (ads.length > 0) {
            displayAds();
            setInterval(rotateAds, {{ kiosk.ads_rotation_interval }} * 1000);
          }
        } catch (error) {
          console.error('Error loading ads:', error);
        }
      }

      function displayAds() {
        const container = document.getElementById('ad-container');
        container.innerHTML = '';

        ads.forEach((ad, index) => {
          let element;
          if (ad.type === 'image') {
            element = document.createElement('img');
            element.src = ad.url;
            element.className = 'ad-slide';
          } else {
            element = document.createElement('video');
            element.src = ad.url;
            element.className = 'ad-slide';
            element.muted = true;
            element.loop = true;
          }

          if (index === 0) {
            element.classList.add('active');
            if (ad.type === 'video') element.play();
          }

          container.appendChild(element);
        });
      }

      function rotateAds() {
        if (ads.length === 0) return;

        const slides = document.querySelectorAll('.ad-slide');
        if (slides.length === 0) return;

        slides[currentAdIndex].classList.remove('active');

        if (slides[currentAdIndex].tagName === 'VIDEO') {
          slides[currentAdIndex].pause();
        }

        currentAdIndex = (currentAdIndex + 1) % ads.length;
        slides[currentAdIndex].classList.add('active');

        if (slides[currentAdIndex].tagName === 'VIDEO') {
          slides[currentAdIndex].play();
        }
      }
      
      // Listen for keyboard input (hardware scanner simulation)
      let scanBuffer = '';
      let scanTimeout;

      document.addEventListener('keypress', (e) => {
        // Only process if not currently showing success animation
        if (isScanning) return;

        clearTimeout(scanTimeout);

        if (e.key === 'Enter') {
          if (scanBuffer.length > 0) {
            processScan(scanBuffer);
            scanBuffer = '';
          }
        } else {
          scanBuffer += e.key;
          scanTimeout = setTimeout(() => {
            scanBuffer = '';
          }, 100);
        }
      });

      async function processScan(qrCode) {
        try {
          const response = await fetch('/api/scan', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ qr_code: qrCode }),
          });

          const data = await response.json();

          if (data.success) {
            // The real-time event system will handle the success animation
            console.log('Scan processed successfully');
          }
        } catch (error) {
          console.error('Error processing scan:', error);
          updateScannerStatus('error', 'Scan Error');
        }
      }

      // Page visibility optimization
      document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
          // Pause ads rotation when page is hidden
          console.log('Page hidden - optimizing performance');
        } else {
          // Resume when page becomes visible
          console.log('Page visible - resuming full functionality');
          if (!eventSource || eventSource.readyState === EventSource.CLOSED) {
            initializeScanner();
          }
        }
      });

      // Initialize everything
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Kiosk initializing...');

        // Initialize scanner connection
        initializeScanner();

        // Load ads
        loadAds();

        // Set initial scanner status
        updateScannerStatus('ready', 'Ready');

        console.log('Kiosk initialized successfully');
      });

      // Cleanup on page unload
      window.addEventListener('beforeunload', function() {
        if (eventSource) {
          eventSource.close();
        }
      });
    </script>
  </body>
</html>

