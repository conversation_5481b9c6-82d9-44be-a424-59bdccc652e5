<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>Kiosk - {{ expo_info.name }}</title>
    
    <!-- CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta20/dist/css/tabler.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/tabler-icons.min.css" rel="stylesheet"/>
    
    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
        --tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe <PERSON>I, Roboto, Helvetica Neue, sans-serif;
      }
      body {
        font-feature-settings: "cv03", "cv04", "cv11";
        margin: 0;
        padding: 0;
        overflow: hidden;
        background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
      }
      
      .kiosk-container {
        display: flex;
        flex-direction: column;
        height: 100vh;
        width: 100vw;
      }
      
      .header-section {
        background: white;
        padding: 1.5rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      }
      
      .ads-section {
        height: 40vh;
        background: #000;
        position: relative;
        overflow: hidden;
      }
      
      .scan-section {
        flex: 1;
        background: white;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
      }
      
      .footer-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 1rem 1.5rem;
        box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
      }
      
      .qr-scanner {
        width: 250px;
        height: 250px;
        border: 4px dashed #667eea;
        border-radius: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 1.5rem 0;
        animation: pulse 2s infinite;
      }
      
      @keyframes pulse {
        0%, 100% { border-color: #667eea; }
        50% { border-color: #764ba2; }
      }
      
      .ad-container {
        width: 100%;
        height: 100%;
        position: relative;
      }
      
      .ad-slide {
        display: none;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .ad-slide.active {
        display: block;
      }
      
      .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
      }
      
      .status-online {
        background: #d1f4e0;
        color: #0f5132;
      }
      
      .pulse-dot {
        width: 6px;
        height: 6px;
        background: #0f5132;
        border-radius: 50%;
        animation: pulse-dot 2s infinite;
      }
      
      @keyframes pulse-dot {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
      
      .scan-counter {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: #667eea;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 600;
        font-size: 0.875rem;
      }
      
      .success-animation {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        width: 100%;
      }
      
      .success-animation.show {
        display: block;
        animation: fadeInOut 3s;
      }
      
      @keyframes fadeInOut {
        0%, 100% { opacity: 0; }
        50% { opacity: 1; }
      }
      
      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
        width: 100%;
      }
      
      .info-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
      }
    </style>
  </head>
  <body>
    <div class="kiosk-container">
      <!-- Header -->
      <div class="header-section">
        <div class="text-center">
          <img src="https://via.placeholder.com/150x50/667eea/ffffff?text=LOGO" alt="Logo" style="height: 50px; margin-bottom: 0.5rem;">
          <h2 class="mb-1" style="color: #667eea; font-size: 1.5rem;">{{ expo_info.name }}</h2>
          <p class="text-muted mb-0" style="font-size: 0.875rem;">
            <i class="ti ti-calendar icon"></i> {{ expo_info.date }}
          </p>
        </div>
      </div>
      
      <!-- Ads Section -->
      <div class="ads-section">
        <div class="ad-container" id="ad-container">
          <!-- Default placeholder if no ads -->
          <div class="ad-slide active" style="display: flex; align-items: center; justify-content: center; background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);">
            <div class="text-center text-white p-4">
              <i class="ti ti-ad icon" style="font-size: 60px;"></i>
              <h3 class="mt-3">Your Ad Here</h3>
              <p>Upload ads in the management panel</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Scan Section -->
      <div class="scan-section">
        <!-- <div class="scan-counter">
          <i class="ti ti-users icon me-1"></i>
          <span id="scan-count">1247</span> Today
        </div> -->
        
        <div id="idle-state" class="text-center">
          <i class="ti ti-qrcode" style="font-size: 100px; color: #667eea; margin-bottom: 1rem;"></i>
          <h2 class="mb-2" style="font-size: 1.75rem;">Welcome!</h2>
          <p class="text-muted h4 mb-3" style="font-size: 1.25rem;">Scan your QR code to print your badge</p>
          
          <div class="qr-scanner">
            <div class="text-center">
              <i class="ti ti-scan icon" style="font-size: 70px; color: #667eea;"></i>
              <p class="mt-2 text-muted">Scanning...</p>
            </div>
          </div>
          
          <div class="alert alert-info d-inline-flex align-items-center" style="font-size: 0.875rem;">
            <i class="ti ti-info-circle icon me-2"></i>
            Position your QR code in front of the scanner
          </div>
        </div>
        
        <div class="success-animation" id="success-state">
          <i class="ti ti-circle-check" style="font-size: 100px; color: #2fb344;"></i>
          <h2 class="mt-3 text-success" style="font-size: 1.75rem;">Success!</h2>
          <p class="h4" style="font-size: 1.25rem;">Your badge is printing...</p>
        </div>
      </div>
      
      <!-- Footer -->
      <!-- <div class="footer-section">
        <div class="info-grid">
          <div class="info-item">
            <i class="ti ti-printer icon text-blue"></i>
            <span class="text-muted">Printer:</span>
            <span class="status-indicator status-online">
              <span class="pulse-dot"></span>
              Online
            </span>
          </div>
          <div class="info-item">
            <i class="ti ti-qrcode icon text-green"></i>
            <span class="text-muted">Scanner:</span>
            <span class="status-indicator status-online">
              <span class="pulse-dot"></span>
              Ready
            </span>
          </div>
          <div class="info-item">
            <i class="ti ti-map-pin icon text-purple"></i>
            <span class="text-muted">{{ expo_info.location }}</span>
          </div>
          <div class="info-item">
            <i class="ti ti-building icon text-orange"></i>
            <span class="text-muted">{{ expo_info.booth_count }}+ Exhibitors</span>
          </div>
        </div>
      </div> -->
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta20/dist/js/tabler.min.js"></script>
    <script>
      // Load and rotate ads
      let currentAdIndex = 0;
      let ads = [];
      
      async function loadAds() {
        try {
          const response = await fetch('/api/ads');
          ads = await response.json();
          
          if (ads.length > 0) {
            displayAds();
            setInterval(rotateAds, {{ kiosk.ads_rotation_interval }} * 1000); // Rotate every 10 seconds
          }
        } catch (error) {
          console.error('Error loading ads:', error);
        }
      }
      
      function displayAds() {
        const container = document.getElementById('ad-container');
        container.innerHTML = '';
        
        ads.forEach((ad, index) => {
          let element;
          if (ad.type === 'image') {
            element = document.createElement('img');
            element.src = ad.url;
            element.className = 'ad-slide';
          } else {
            element = document.createElement('video');
            element.src = ad.url;
            element.className = 'ad-slide';
            element.muted = true;
            element.loop = true;
          }
          
          if (index === 0) {
            element.classList.add('active');
            if (ad.type === 'video') element.play();
          }
          
          container.appendChild(element);
        });
      }
      
      function rotateAds() {
        if (ads.length === 0) return;
        
        const slides = document.querySelectorAll('.ad-slide');
        slides[currentAdIndex].classList.remove('active');
        
        if (slides[currentAdIndex].tagName === 'VIDEO') {
          slides[currentAdIndex].pause();
        }
        
        currentAdIndex = (currentAdIndex + 1) % ads.length;
        slides[currentAdIndex].classList.add('active');
        
        if (slides[currentAdIndex].tagName === 'VIDEO') {
          slides[currentAdIndex].play();
        }
      }
      
      // Simulate QR scan (for demo purposes)
      function simulateScan() {
        const idleState = document.getElementById('idle-state');
        const successState = document.getElementById('success-state');
        
        idleState.style.display = 'none';
        successState.classList.add('show');
        
        // Update counter
        const counter = document.getElementById('scan-count');
        counter.textContent = parseInt(counter.textContent) + 1;
        
        setTimeout(() => {
          successState.classList.remove('show');
          idleState.style.display = 'block';
        }, 3000);
      }
      
      // Listen for keyboard input (simulating scanner)
      let scanBuffer = '';
      let scanTimeout;
      
      document.addEventListener('keypress', (e) => {
        clearTimeout(scanTimeout);
        
        if (e.key === 'Enter') {
          if (scanBuffer.length > 0) {
            processScan(scanBuffer);
            scanBuffer = '';
          }
        } else {
          scanBuffer += e.key;
          scanTimeout = setTimeout(() => {
            scanBuffer = '';
          }, 100);
        }
      });
      
      async function processScan(qrCode) {
        try {
          const response = await fetch('/api/scan', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ qr_code: qrCode }),
          });
          
          const data = await response.json();
          
          if (data.success) {
            simulateScan();
          }
        } catch (error) {
          console.error('Error processing scan:', error);
        }
      }
      
      // Initialize
      loadAds();
      
      // Auto-refresh stats every 30 seconds
      setInterval(async () => {
        try {
          const response = await fetch('/api/stats');
          const stats = await response.json();
          document.getElementById('scan-count').textContent = stats.today_scans;
        } catch (error) {
          console.error('Error updating stats:', error);
        }
      }, 30000);
    </script>
  </body>
</html>

