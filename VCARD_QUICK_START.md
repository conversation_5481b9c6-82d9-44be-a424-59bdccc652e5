# vCard Integration - Quick Start

## 🎯 What Changed

The kiosk now processes **vCard QR codes** instead of email addresses, and sends them to your API with the event code.

---

## ⚡ Quick Setup (3 Steps)

### Step 1: Set Your Event Code

Edit `constants.py`:

```python
FAIR_CODE = "EVENTCODE2025"  # ← Change to your event code
```

### Step 2: Configure Your API Endpoint

Edit `app.py` (line ~306):

```python
api_url = 'http://YOUR_SERVER/api/v1/process-vcard'  # ← Your API URL
```

### Step 3: Test It!

```bash
# Start the app
python app.py

# Open kiosk view
# http://localhost:5000/kiosk/horizontal

# Type a vCard and press Enter (for testing)
```

---

## 📡 What Gets Sent to Your API

**Method**: `POST`

**URL**: Your configured endpoint

**Headers**:
```json
{
  "x-api-key": "31d051aa-7c55-4dad-91f8-e631c0f4af3a",
  "Content-Type": "application/json"
}
```

**Body**:
```json
{
  "vbarcode": "BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nEMAIL:<EMAIL>\nEND:VCARD",
  "faircode": "EVENTCODE2025"
}
```

---

## 📋 Expected API Response

**Success** (200 OK):
```json
{
  "success": true,
  "participant": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "company": "ABC Corporation",
    "position": "CEO",
    "phone": "+1234567890",
    "country": "Philippines",
    "badge_type": "VIP"
  }
}
```

**Error** (404):
```json
{
  "error": "Participant not found"
}
```

---

## 🧪 Test Without Hardware

### Method 1: Keyboard Test

1. Open: http://localhost:5000/kiosk/horizontal
2. Type any text (simulating vCard)
3. Press **Enter**
4. Check browser console (F12) for request/response

### Method 2: cURL Test

```bash
curl -X POST http://localhost:5000/api/process-vcard \
  -H "Content-Type: application/json" \
  -d '{"vcard": "BEGIN:VCARD\nVERSION:3.0\nFN:Test User\nEMAIL:<EMAIL>\nEND:VCARD"}'
```

---

## 🔧 Configuration Checklist

- [ ] Set `FAIR_CODE` in `constants.py`
- [ ] Set API endpoint URL in `app.py`
- [ ] Set API key in `app.py` (if different)
- [ ] Configure COM port in Settings page
- [ ] Test with keyboard simulation
- [ ] Test with real vCard QR code

---

## 📁 Files Modified

1. **`constants.py`** - Added `FAIR_CODE = "EVENTCODE2025"`
2. **`app.py`** - Changed endpoint from `/api/buyer/<email>` to `/api/process-vcard` (POST)
3. **`templates/kiosk_horizontal.html`** - Updated to send vCard data via POST

---

## 🔍 Key Differences from Previous Version

| Before | After |
|--------|-------|
| Scanned email addresses | Scans vCard QR codes |
| GET request to `/api/buyer/<email>` | POST request to `/api/process-vcard` |
| Sent only email | Sends `vbarcode` + `faircode` |
| No event code | Includes event code from constants |

---

## 📞 Need More Info?

See **`VCARD_INTEGRATION_GUIDE.md`** for complete documentation including:
- Detailed workflow
- vCard format examples
- Troubleshooting guide
- Customization options
- Security notes

---

## ✅ You're Ready!

The system is now configured to:
- ✅ Scan vCard QR codes
- ✅ Send to your API with event code
- ✅ Display participant information
- ✅ Print badges automatically

**Happy scanning! 🎉**

