# Quick Testing Guide - QR Scanner Integration

## 🧪 How to Test the System

### Method 1: Keyboard Simulation (No Hardware Needed)

This is the easiest way to test without a physical QR scanner.

1. **Start the Flask app**:
   ```bash
   python app.py
   ```

2. **Open the kiosk view**:
   ```
   http://localhost:5000/kiosk/horizontal
   ```

3. **Press F11** for fullscreen mode

4. **Type an email address** (the QR code value):
   ```
   <EMAIL>
   ```

5. **Press Enter**

6. **Expected Result**:
   - If API is running and email exists:
     - ✅ Green checkmark appears
     - ✅ "Participant Found!" message
     - ✅ Buyer information displayed
     - ✅ "Your badge is printing..." message
     - ✅ Auto-closes after 5 seconds
   
   - If email not found or API error:
     - ❌ Red alert icon appears
     - ❌ "Not Found" or error message
     - ❌ Auto-closes after 3 seconds

---

### Method 2: Test API Endpoints Directly

#### Test 1: Check if barcode endpoint works

```bash
curl http://localhost:5000/api/barcode
```

**Expected Response**:
```json
{
  "barcode": null
}
```
(null if nothing scanned yet)

#### Test 2: Test buyer lookup endpoint

```bash
curl http://localhost:5000/api/buyer/<EMAIL>
```

**Expected Response** (if API server is running):
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "company": "ABC Corporation",
  ...
}
```

**Expected Response** (if API server is NOT running):
```json
{
  "error": "Connection refused"
}
```

---

### Method 3: Test with Hardware Scanner

1. **Configure COM Port**:
   - Go to http://localhost:5000/settings
   - Enter COM port number (e.g., 3 for COM3)
   - Click "Save Settings"

2. **Restart Flask app**:
   ```bash
   python app.py
   ```

3. **Check console output**:
   ```
   Serial port COM3 opened successfully
   ```

4. **Scan a QR code** with email address

5. **Check console output**:
   ```
   Barcode scanned: <EMAIL>
   ```

6. **Kiosk should automatically**:
   - Detect the scan
   - Call the API
   - Display results

---

## 🔧 Troubleshooting

### Issue 1: "Error connecting to the system"

**Cause**: External API server is not running

**Solution**:
1. Check if API server is running at `http://127.0.0.1/api_famev2/`
2. Test API directly:
   ```bash
   curl -H "x-api-key: 31d051aa-7c55-4dad-91f8-e631c0f4af3a" \
        http://127.0.0.1/api_famev2/v1/buyers/email/<EMAIL>
   ```
3. If API is down, start it first

### Issue 2: "Participant not found"

**Cause**: Email doesn't exist in database

**Solution**:
1. Use a valid email that exists in your database
2. Check API response manually
3. Verify email format is correct

### Issue 3: Scanner not detecting

**Cause**: COM port not configured or wrong port

**Solution**:
1. Check Device Manager → Ports (COM & LPT)
2. Find your scanner's COM port number
3. Update in Settings page
4. Restart Flask app

### Issue 4: Nothing happens when typing

**Cause**: JavaScript not loaded or errors

**Solution**:
1. Open browser console (F12)
2. Check for JavaScript errors
3. Verify `pollBarcode` function is running
4. Check network tab for API calls

---

## 📊 Expected API Response Format

Your external API should return JSON in this format:

```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "company": "ABC Corporation",
  "position": "CEO",
  "phone": "+1234567890",
  "country": "Philippines",
  "badge_type": "VIP"
}
```

**Supported Fields** (all optional):
- `name` - Participant name
- `email` - Email address
- `company` - Company name
- `position` - Job title
- `phone` - Phone number
- `country` - Country
- `badge_type` - Badge type (VIP, Regular, etc.)

---

## 🎯 Quick Test Checklist

### Before Event

- [ ] Flask app starts without errors
- [ ] Settings page loads
- [ ] COM port configured correctly
- [ ] Kiosk view loads in browser
- [ ] Keyboard test works (type email + Enter)
- [ ] API endpoint responds
- [ ] Buyer info displays correctly
- [ ] Success animation shows
- [ ] Error handling works
- [ ] Scanner hardware connected
- [ ] Scanner detects QR codes
- [ ] Badge printer connected
- [ ] Badge printer prints

### During Event

- [ ] Monitor Flask console for errors
- [ ] Check scan counter increments
- [ ] Verify buyer info accuracy
- [ ] Test error recovery
- [ ] Monitor API response times
- [ ] Check printer status

---

## 🚀 Quick Start Commands

```bash
# 1. Activate virtual environment
kiosk1\Scripts\activate

# 2. Start Flask app
python app.py

# 3. Open kiosk in browser
# Navigate to: http://localhost:5000/kiosk/horizontal

# 4. Test with keyboard
# Type: <EMAIL>
# Press: Enter

# 5. Check console output
# Should see: "Barcode scanned: <EMAIL>"
```

---

## 📝 Test Scenarios

### Scenario 1: Valid Participant

**Input**: `<EMAIL>`

**Expected**:
- ✅ API returns 200 OK
- ✅ Buyer info displayed
- ✅ Success animation
- ✅ Badge prints
- ✅ Counter increments

### Scenario 2: Invalid Email

**Input**: `<EMAIL>`

**Expected**:
- ❌ API returns 404
- ❌ Error message shown
- ❌ "Participant not found"
- ❌ No badge printed
- ❌ Counter unchanged

### Scenario 3: API Server Down

**Input**: Any email

**Expected**:
- ❌ Connection error
- ❌ "Error connecting to the system"
- ❌ Error animation
- ❌ Returns to idle after 3 seconds

### Scenario 4: Rapid Scans

**Input**: Scan multiple QR codes quickly

**Expected**:
- ✅ Each scan processed individually
- ✅ No duplicate processing
- ✅ Proper queue handling
- ✅ All scans counted

---

## 🔍 Debugging Tips

### Enable Verbose Logging

Add to `app.py`:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Check Browser Console

Press F12 and look for:
- Network requests to `/api/barcode`
- Network requests to `/api/buyer/...`
- JavaScript errors
- Console.log messages

### Monitor Flask Console

Watch for:
- "Barcode scanned: ..."
- API request logs
- Error messages
- Serial port status

### Test API Independently

```python
import requests

url = 'http://127.0.0.1/api_famev2/v1/buyers/email/<EMAIL>'
headers = {
    'x-api-key': '31d051aa-7c55-4dad-91f8-e631c0f4af3a',
    'Accept': 'application/json'
}

response = requests.get(url, headers=headers)
print(response.status_code)
print(response.json())
```

---

## ✅ Success Indicators

You'll know the system is working when:

1. **Console shows**:
   ```
   Serial port COM3 opened successfully
   * Running on http://0.0.0.0:5000
   ```

2. **Keyboard test works**:
   - Type email + Enter
   - Info appears on screen

3. **Scanner test works**:
   - Scan QR code
   - Console shows: "Barcode scanned: ..."
   - Info appears on screen

4. **API test works**:
   ```bash
   curl http://localhost:5000/api/buyer/<EMAIL>
   # Returns JSON with buyer info
   ```

---

## 📞 Need Help?

Check these files for more information:
- `QR_SCANNER_API_GUIDE.md` - Complete integration guide
- `README.md` - General documentation
- `QUICK_START.md` - Quick start guide
- `DEPLOYMENT_CHECKLIST.md` - Production deployment

---

**Happy Testing! 🎉**

