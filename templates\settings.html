{% extends "base.html" %}

{% block title %}Settings - Kiosk Management System{% endblock %}

{% block page_header %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          Settings
        </h2>
        <div class="text-muted mt-1">Configure your kiosk system preferences</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <!-- <button class="btn btn-primary" onclick="saveSettings()">
            <i class="ti ti-device-floppy icon"></i>
            Save Changes
          </button> -->
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block content %}

<div class="row row-deck row-cards">
  <!-- Scanner Settings -->
  <form id="settingsForm" method="POST" action="{{ url_for('update_settings') }}">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Scanner Settings - KIOSK ID {{kiosk.kiosk_id}}</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="mb-3">
              <div class="col-md-12">
                <label class="form-label">Com Port</label>
                <input type="text" class="form-control" value="{{kiosk.scanner_com_port}}" name="scanner_com_port" placeholder="Enter com port number (e.g. COM3)">
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">Scanner Status</label>
              <div class="d-flex align-items-center">
                <span id="scanner-status-badge" class="badge bg-secondary me-2">Checking...</span>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="checkScannerStatus()">
                  <i class="ti ti-refresh icon"></i>
                  Check Status
                </button>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">Scanner Actions</label>
              <div class="btn-group w-100" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="testScannerConnection()">
                  <i class="ti ti-test-pipe icon"></i>
                  Test
                </button>
                <button type="button" class="btn btn-outline-warning" onclick="restartScannerConnection()">
                  <i class="ti ti-reload icon"></i>
                  Restart
                </button>
                <a href="{{ url_for('scanner_status_page') }}" class="btn btn-outline-info">
                  <i class="ti ti-eye icon"></i>
                  Monitor
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-md-12">
            <div class="alert alert-info">
              <i class="ti ti-info-circle icon me-2"></i>
              <strong>Note:</strong> Changes to Scanner settings may require system restart. Use the actions above to test and restart the scanner connection.
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <div class="d-flex">
          <!-- <button class="btn btn-link">Reset to Defaults</button> -->
          <button class="btn btn-primary ms-auto" type="submit">Save Settings</button>
        </div>
      </div>
    </div>
  </div>
  </form>

  <!-- General Settings -->
   <form id="generalSettingsForm" method="POST" action="{{ url_for('update_general_settings') }}">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">General Settings</h3>
      </div>
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-12">
            <label class="form-label">Kiosk ID</label>
            <input type="text" class="form-control" name="kiosk_id" value="{{kiosk.kiosk_id}}" placeholder="Enter event name">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label class="form-label">Event Name</label>
            <input type="text" class="form-control" name="event_name" value="{{kiosk.event_name}}" placeholder="Enter event name">
          </div>
          <div class="col-md-6">
            <label class="form-label">Event Location</label>
            <input type="text" class="form-control" name="event_location" value="{{kiosk.event_location}}" placeholder="Enter location">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label class="form-label">Event Start Date</label>
            <input type="date" class="form-control" name="event_start_date" value="{{kiosk.event_start_date}}">
          </div>
          <div class="col-md-6">
            <label class="form-label">Event End Date</label>
            <input type="date" class="form-control" name="event_end_date" value="{{kiosk.event_end_date}}">
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label class="form-label">Welcome Message</label>
            <textarea class="form-control" rows="3" name="welcome_message" placeholder="Enter welcome message">{{kiosk.welcome_message}}</textarea>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <div class="d-flex">
          <!-- <button class="btn btn-link">Reset to Defaults</button> -->
          <button class="btn btn-primary ms-auto" type="submit">Save Settings</button>
        </div>
      </div>
    </div>
  </div>
  </form>

  <!-- Kiosk Images -->
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Kiosk Images</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">Header Image</label>
              <input type="file" class="form-control" id="header-upload" accept="image/*">
              <small class="form-hint">Recommended size: 1920x180px (Full width banner)</small>
            </div>
            <div class="mb-3">
              <button type="button" class="btn btn-primary" onclick="uploadHeaderImage()">
                <i class="ti ti-upload icon me-2"></i>
                Upload Header
              </button>
              <span id="header-status" class="ms-2"></span>
            </div>
            <div class="mb-3">
              <img id="header-preview" src="/static/uploads/header.jpg?t={{ range(1, 10000) | random }}" alt="Header Preview"
                   style="max-width: 100%; height: auto; border-radius: 0.5rem; display: none;"
                   onload="this.style.display='block'" onerror="this.style.display='none'">
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">Event Schedule Image</label>
              <input type="file" class="form-control" id="schedule-upload" accept="image/*">
              <small class="form-hint">Recommended size: 800x300px (Schedule display)</small>
            </div>
            <div class="mb-3">
              <button type="button" class="btn btn-primary" onclick="uploadScheduleImage()">
                <i class="ti ti-upload icon me-2"></i>
                Upload Schedule
              </button>
              <span id="schedule-status" class="ms-2"></span>
            </div>
            <div class="mb-3">
              <img id="schedule-preview" src="/static/uploads/schedule.jpg?t={{ range(1, 10000) | random }}" alt="Schedule Preview"
                   style="max-width: 100%; height: auto; border-radius: 0.5rem; display: none;"
                   onload="this.style.display='block'" onerror="this.style.display='none'">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <form id="apiSettingsForm" method="POST" action="{{ url_for('update_api_settings') }}">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">API Settings</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-8">
            <div class="mb-3">
              <div class="col-md-12">
                <label class="form-label">API Url</label>
                <input type="text" class="form-control" value="{{kiosk.api_url}}" name="api_url" placeholder="Enter API Url">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <div class="d-flex">
          <!-- <button class="btn btn-link">Reset to Defaults</button> -->
          <button class="btn btn-primary ms-auto" type="submit">Save Settings</button>
        </div>
      </div>
    </div>
  </div>
  </form>

  



  <!-- Kiosk Display Settings -->
  <!-- <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Kiosk Display Settings</h3>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label class="form-label">Display Orientation</label>
          <select class="form-select">
            <option value="horizontal" selected>Horizontal (Landscape)</option>
            <option value="vertical">Vertical (Portrait)</option>
            <option value="auto">Auto-detect</option>
          </select>
        </div>
        <div class="mb-3">
          <label class="form-label">Idle Timeout (seconds)</label>
          <input type="number" class="form-control" value="30" placeholder="30">
          <small class="form-hint">Time before returning to idle screen</small>
        </div>
        <div class="mb-3">
          <label class="form-label">Ad Rotation Interval (seconds)</label>
          <input type="number" class="form-control" value="10" placeholder="10">
          <small class="form-hint">Time between ad transitions</small>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Enable Screensaver</span>
          </label>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Show Scan Counter</span>
          </label>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Play Sound on Scan</span>
          </label>
        </div>
      </div>
    </div>
  </div> -->

  <!-- Printer Settings -->
  <!-- <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Printer Settings</h3>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label class="form-label">Printer Name</label>
          <select class="form-select">
            <option value="default" selected>Default Printer</option>
            <option value="zebra_zd420">Zebra ZD420</option>
            <option value="dymo_450">DYMO LabelWriter 450</option>
            <option value="brother_ql">Brother QL-820NWB</option>
          </select>
        </div>
        <div class="mb-3">
          <label class="form-label">Badge Size</label>
          <select class="form-select">
            <option value="4x6" selected>4" x 6" (Standard)</option>
            <option value="3x4">3" x 4" (Small)</option>
            <option value="4x3">4" x 3" (Landscape)</option>
          </select>
        </div>
        <div class="mb-3">
          <label class="form-label">Print Quality</label>
          <select class="form-select">
            <option value="draft">Draft (Fast)</option>
            <option value="normal" selected>Normal</option>
            <option value="high">High Quality</option>
          </select>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Auto-print on Scan</span>
          </label>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox">
            <span class="form-check-label">Print Duplicate Copy</span>
          </label>
        </div>
        <div class="mt-3">
          <button class="btn btn-outline-primary w-100">
            <i class="ti ti-printer icon me-2"></i>
            Test Print
          </button>
        </div>
      </div>
    </div>
  </div> -->

  <!-- Scanner Settings -->
  <!-- <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Scanner Settings</h3>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label class="form-label">Scanner Type</label>
          <select class="form-select">
            <option value="usb" selected>USB Scanner</option>
            <option value="camera">Camera Scanner</option>
            <option value="bluetooth">Bluetooth Scanner</option>
          </select>
        </div>
        <div class="mb-3">
          <label class="form-label">Scan Timeout (seconds)</label>
          <input type="number" class="form-control" value="5" placeholder="5">
          <small class="form-hint">Maximum time to wait for scan</small>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Enable Beep on Scan</span>
          </label>
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Validate QR Code Format</span>
          </label>
        </div>
        <div class="mt-3">
          <button class="btn btn-outline-primary w-100">
            <i class="ti ti-qrcode icon me-2"></i>
            Test Scanner
          </button>
        </div>
      </div>
    </div>
  </div> -->

  <!-- Network Settings -->
  <!-- <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Network & API Settings</h3>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label class="form-label">API Endpoint</label>
          <input type="url" class="form-control" value="http://localhost:5000/api" placeholder="Enter API URL">
        </div>
        <div class="mb-3">
          <label class="form-label">API Key</label>
          <input type="password" class="form-control" value="••••••••••••" placeholder="Enter API key">
        </div>
        <div class="mb-3">
          <label class="form-label">Connection Timeout (seconds)</label>
          <input type="number" class="form-control" value="10" placeholder="10">
        </div>
        <div class="mb-3">
          <label class="form-check form-switch">
            <input class="form-check-input" type="checkbox" checked>
            <span class="form-check-label">Enable SSL/TLS</span>
          </label>
        </div>
        <div class="mt-3">
          <button class="btn btn-outline-primary w-100">
            <i class="ti ti-plug icon me-2"></i>
            Test Connection
          </button>
        </div>
      </div>
    </div>
  </div> -->

  <!-- Advanced Settings -->
  <!-- <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Advanced Settings</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-check form-switch">
                <input class="form-check-input" type="checkbox" checked>
                <span class="form-check-label">Enable Logging</span>
              </label>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-check form-switch">
                <input class="form-check-input" type="checkbox">
                <span class="form-check-label">Debug Mode</span>
              </label>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label class="form-check form-switch">
                <input class="form-check-input" type="checkbox" checked>
                <span class="form-check-label">Auto-update</span>
              </label>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-md-12">
            <div class="alert alert-info">
              <i class="ti ti-info-circle icon me-2"></i>
              <strong>Note:</strong> Changes to advanced settings may require system restart.
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <div class="d-flex">
          <button class="btn btn-link">Reset to Defaults</button>
          <button class="btn btn-primary ms-auto" onclick="saveSettings()">Save All Settings</button>
        </div>
      </div>
    </div>
  </div> -->
</div>
{% endblock %}

{% block extra_js %}

<script>
document.getElementById("settingsForm").addEventListener("submit", function(event) {
  event.preventDefault(); // Stop normal form submission

  const formData = new FormData(this);

  fetch("/update_settings", {
    method: "POST",
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    // Success or error message using sweetalert2
    if (data.message)
    {
      showAlert(data.message, 'Settings Updated', 'success');
    }
    else if (data.error) {
      showAlert(data.error, 'Error', 'error');
    } else {
      showAlert('Unknown response from server.', 'Warning', 'warning');
    } 
  })
  .catch(error => {
    showAlert("❌ Network error: " + error, 'error');
  });
});

document.getElementById("generalSettingsForm").addEventListener("submit", function(event) {
  event.preventDefault(); // Stop normal form submission

  const formData = new FormData(this);

  fetch("/update_general_settings", {
    method: "POST",
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.message)
    {
      // sweet alert with confirm to refresh page
      Swal.fire({
        title: 'Settings Updated',
        text: data.message,
        icon: 'success',
        confirmButtonText: 'OK'
      }).then((result) => {
        if (result.isConfirmed) {
          location.reload();
        }
      });
    }
    else if (data.error) {
      showAlert(data.error, 'Error', 'error');
    } else {
      showAlert('Unknown response from server.', 'Warning', 'warning');
    } 
  })
  .catch(error => {
    showAlert("❌ Network error: " + error, 'error');
  });
});

document.getElementById("apiSettingsForm").addEventListener("submit", function(event) {
  event.preventDefault(); // Stop normal form submission

  const formData = new FormData(this);

  fetch("/update_api_settings", {
    method: "POST",
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    // Success or error message using sweetalert2
    if (data.message)
    {
      showAlert(data.message, 'API Settings Updated', 'success');
    }
    else if (data.error) {
      showAlert(data.error, 'Error', 'error');
    } else {
      showAlert('Unknown response from server.', 'Warning', 'warning');
    } 
  })
  .catch(error => {
    showAlert("❌ Network error: " + error, 'error');
  });
});


// Upload Header Image
async function uploadHeaderImage() {
  const fileInput = document.getElementById('header-upload');
  const statusSpan = document.getElementById('header-status');

  if (!fileInput.files || fileInput.files.length === 0) {
    statusSpan.innerHTML = '<span class="text-danger">Please select a file</span>';
    return;
  }

  const formData = new FormData();
  formData.append('file', fileInput.files[0]);

  statusSpan.innerHTML = '<span class="text-info">Uploading...</span>';

  try {
    const response = await fetch('/upload/header', {
      method: 'POST',
      body: formData
    });

    const data = await response.json();

    if (data.success) {
      statusSpan.innerHTML = '<span class="text-success"><i class="ti ti-check icon"></i> Uploaded successfully!</span>';
      // Reload preview with cache buster
      const preview = document.getElementById('header-preview');
      preview.src = '/static/uploads/header.jpg?t=' + new Date().getTime();
      preview.style.display = 'block';

      setTimeout(() => {
        statusSpan.innerHTML = '';
      }, 3000);
    } else {
      statusSpan.innerHTML = '<span class="text-danger">' + data.message + '</span>';
    }
  } catch (error) {
    statusSpan.innerHTML = '<span class="text-danger">Upload failed</span>';
  }
}

// Upload Schedule Image
async function uploadScheduleImage() {
  const fileInput = document.getElementById('schedule-upload');
  const statusSpan = document.getElementById('schedule-status');

  if (!fileInput.files || fileInput.files.length === 0) {
    statusSpan.innerHTML = '<span class="text-danger">Please select a file</span>';
    return;
  }

  const formData = new FormData();
  formData.append('file', fileInput.files[0]);

  statusSpan.innerHTML = '<span class="text-info">Uploading...</span>';

  try {
    const response = await fetch('/upload/schedule', {
      method: 'POST',
      body: formData
    });

    const data = await response.json();

    if (data.success) {
      statusSpan.innerHTML = '<span class="text-success"><i class="ti ti-check icon"></i> Uploaded successfully!</span>';
      // Reload preview with cache buster
      const preview = document.getElementById('schedule-preview');
      preview.src = '/static/uploads/schedule.jpg?t=' + new Date().getTime();
      preview.style.display = 'block';

      setTimeout(() => {
        statusSpan.innerHTML = '';
      }, 3000);
    } else {
      statusSpan.innerHTML = '<span class="text-danger">' + data.message + '</span>';
    }
  } catch (error) {
    statusSpan.innerHTML = '<span class="text-danger">Upload failed</span>';
  }
}

// Scanner management functions
function checkScannerStatus() {
  const badge = document.getElementById('scanner-status-badge');
  badge.textContent = 'Checking...';
  badge.className = 'badge bg-secondary me-2';

  fetch('/api/scanner/status')
    .then(response => response.json())
    .then(data => {
      updateScannerStatusBadge(data.status);
    })
    .catch(error => {
      console.error('Error checking scanner status:', error);
      badge.textContent = 'Error';
      badge.className = 'badge bg-danger me-2';
    });
}

function updateScannerStatusBadge(status) {
  const badge = document.getElementById('scanner-status-badge');

  switch(status) {
    case 'Ready':
      badge.textContent = 'Ready';
      badge.className = 'badge bg-success me-2';
      break;
    case 'Connected':
      badge.textContent = 'Connected';
      badge.className = 'badge bg-info me-2';
      break;
    case 'Scanning':
      badge.textContent = 'Scanning';
      badge.className = 'badge bg-warning me-2';
      break;
    case 'Disconnected':
      badge.textContent = 'Disconnected';
      badge.className = 'badge bg-secondary me-2';
      break;
    case 'Not Configured':
      badge.textContent = 'Not Configured';
      badge.className = 'badge bg-warning me-2';
      break;
    case 'Error':
    default:
      badge.textContent = 'Error';
      badge.className = 'badge bg-danger me-2';
      break;
  }
}

function testScannerConnection() {
  fetch('/api/scanner/test', { method: 'POST' })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        Swal.fire({
          title: 'Scanner Test',
          text: data.message,
          icon: 'success',
          confirmButtonText: 'OK'
        });
        updateScannerStatusBadge(data.status);
      } else {
        Swal.fire({
          title: 'Scanner Test Failed',
          text: data.message,
          icon: 'error',
          confirmButtonText: 'OK'
        });
      }
    })
    .catch(error => {
      Swal.fire({
        title: 'Test Error',
        text: 'Failed to test scanner connection',
        icon: 'error',
        confirmButtonText: 'OK'
      });
    });
}

function restartScannerConnection() {
  Swal.fire({
    title: 'Restart Scanner?',
    text: 'This will restart the scanner connection. Continue?',
    icon: 'question',
    showCancelButton: true,
    confirmButtonText: 'Yes, restart',
    cancelButtonText: 'Cancel'
  }).then((result) => {
    if (result.isConfirmed) {
      fetch('/api/scanner/restart', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            Swal.fire({
              title: 'Scanner Restarted',
              text: data.message,
              icon: 'success',
              confirmButtonText: 'OK'
            });
            updateScannerStatusBadge(data.status);
          } else {
            Swal.fire({
              title: 'Restart Failed',
              text: data.message,
              icon: 'error',
              confirmButtonText: 'OK'
            });
          }
        })
        .catch(error => {
          Swal.fire({
            title: 'Restart Error',
            text: 'Failed to restart scanner',
            icon: 'error',
            confirmButtonText: 'OK'
          });
        });
    }
  });
}

// Initialize scanner status on page load
document.addEventListener('DOMContentLoaded', function() {
  checkScannerStatus();

  // Update scanner status every 10 seconds
  setInterval(checkScannerStatus, 10000);
});

</script>

{% endblock %}

