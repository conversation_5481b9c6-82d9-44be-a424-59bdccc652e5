<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>{% block title %}Kiosk Management System{% endblock %}</title>

    <!-- CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta20/dist/css/tabler.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/tabler-icons.min.css" rel="stylesheet"/>

    <!-- Custom CSS -->
    <!-- <link href="{{ url_for('static', filename='mycss.css') }}" rel="stylesheet"/> -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
        --tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
      }
      body {
        font-feature-settings: "cv03", "cv04", "cv11";
      }
    </style>

    {% block extra_css %}{% endblock %}
  </head>
  <body>
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta20/dist/js/tabler.min.js"></script>

    <div class="page">
      {% block navbar %}
      <!-- Navbar -->
      <header class="navbar navbar-expand-md d-print-none">
        <div class="container-xl">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
            <a href="/">
              <i class="ti ti-qrcode icon icon-lg"></i>
              Kiosk System
            </a>
          </h1>
          <div class="navbar-nav flex-row order-md-last">
            <div class="nav-item d-none d-md-flex me-3">
              <div class="btn-list">
                <a href="{{ url_for('kiosk_horizontal') }}" class="btn" target="_blank">
                  <i class="ti ti-device-desktop icon"></i>
                  Kiosk View
                </a>
              </div>
            </div>
            <!-- <div class="d-none d-md-flex">
              <a href="?theme=dark" class="nav-link px-0 hide-theme-dark" title="Enable dark mode" data-bs-toggle="tooltip" data-bs-placement="bottom">
                <i class="ti ti-moon icon"></i>
              </a>
              <a href="?theme=light" class="nav-link px-0 hide-theme-light" title="Enable light mode" data-bs-toggle="tooltip" data-bs-placement="bottom">
                <i class="ti ti-sun icon"></i>
              </a>
            </div> -->
            <div class="ribbon">KIOISK ID: {{ kiosk.kiosk_id }}</div>
          </div>
        </div>
      </header>

      <header class="navbar-expand-md">
        <div class="collapse navbar-collapse" id="navbar-menu">
          <div class="navbar">
            <div class="container-xl">
              <ul class="navbar-nav">
                <li class="nav-item {% if request.endpoint == 'index' %}active{% endif %}">
                  <a class="nav-link" href="{{ url_for('index') }}">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <i class="ti ti-home icon"></i>
                    </span>
                    <span class="nav-link-title">Home</span>
                  </a>
                </li>
                <li class="nav-item {% if request.endpoint == 'dashboard' %}active{% endif %}">
                  <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <i class="ti ti-dashboard icon"></i>
                    </span>
                    <span class="nav-link-title">Dashboard</span>
                  </a>
                </li>
                <li class="nav-item {% if request.endpoint == 'ads_management' %}active{% endif %}">
                  <a class="nav-link" href="{{ url_for('ads_management') }}">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <i class="ti ti-ad icon"></i>
                    </span>
                    <span class="nav-link-title">Ads Management</span>
                  </a>
                </li>
                <li class="nav-item {% if request.endpoint == 'scanner_status_page' %}active{% endif %}">
                  <a class="nav-link" href="{{ url_for('scanner_status_page') }}">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <i class="ti ti-qrcode icon"></i>
                    </span>
                    <span class="nav-link-title">Scanner Status</span>
                  </a>
                </li>
                <li class="nav-item dropdown">
                  <a class="nav-link dropdown-toggle {% if request.endpoint in ['kiosk_horizontal', 'kiosk_vertical'] %}active{% endif %}" href="#navbar-kiosk" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <i class="ti ti-device-desktop icon"></i>
                    </span>
                    <span class="nav-link-title">Kiosk Views</span>
                  </a>
                  <div class="dropdown-menu">
                    <a class="dropdown-item" href="{{ url_for('kiosk_horizontal') }}" target="_blank">
                      <i class="ti ti-device-desktop icon me-2"></i>
                      Horizontal Monitor
                    </a>
                    <a class="dropdown-item" href="{{ url_for('kiosk_vertical') }}" target="_blank">
                      <i class="ti ti-device-mobile icon me-2"></i>
                      Vertical Monitor
                    </a>
                  </div>
                </li>
                <li class="nav-item {% if request.endpoint == 'settings2' %}active{% endif %}">
                  <a class="nav-link" href="{{ url_for('settings') }}">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <i class="ti ti-settings icon"></i>
                    </span>
                    <span class="nav-link-title">Settings</span>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </header>
      {% endblock %}

      <div class="page-wrapper">
        {% block page_header %}{% endblock %}

        <!-- Page body -->
        <div class="page-body">
          <div class="container-xl">
            {% with messages = get_flashed_messages(with_categories=true) %}
              {% if messages %}
                {% for category, message in messages %}
                  <div class="alert alert-{{ category }} alert-dismissible" role="alert">
                    <div class="d-flex">
                      <div>
                        {% if category == 'success' %}
                          <i class="ti ti-check icon alert-icon"></i>
                        {% elif category == 'danger' %}
                          <i class="ti ti-alert-circle icon alert-icon"></i>
                        {% else %}
                          <i class="ti ti-info-circle icon alert-icon"></i>
                        {% endif %}
                      </div>
                      <div>{{ message }}</div>
                    </div>
                    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                  </div>
                {% endfor %}
              {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
          </div>
        </div>

        {% block footer %}
        <footer class="footer footer-transparent d-print-none">
          <div class="container-xl">
            <div class="row text-center align-items-center flex-row-reverse">
              <div class="col-lg-auto ms-lg-auto">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item">
                    <a href="#" class="link-secondary">Documentation</a>
                  </li>
                  <li class="list-inline-item">
                    <a href="#" class="link-secondary">Support</a>
                  </li>
                </ul>
              </div>
              <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item">
                    Copyright &copy; 2025
                    <a href="." class="link-secondary">Kiosk Management System</a>.
                    All rights reserved.
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </footer>
        {% endblock %}
      </div>
    </div>

    <!-- Libs JS -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta20/dist/libs/apexcharts/dist/apexcharts.min.js" defer></script>

    <!-- Custom JS -->
    <!-- <script src="{{ url_for('static', filename='my.js') }}"></script> -->

    <script>

      function showAlert(message, title, type = 'info') {
        Swal.fire({
          icon: type,
          title: title,
          text: message
        });
      }

      function showConfirmAlert(message, callback) {
        Swal.fire({
          title: 'Are you sure?',
          text: message,
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes'
        }).then((result) => {
          if (result.isConfirmed) {
            callback();
          }
        });
      }

      function showLoading() {
        Swal.fire({
          title: 'Loading...',
          html: 'Please wait',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });
      }

      function hideLoading() {
        Swal.close();
      }

    </script>

    {% block extra_js %}{% endblock %}
  </body>
</html>