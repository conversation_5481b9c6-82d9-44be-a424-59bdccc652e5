# Deployment Checklist - Kiosk Management System

## 📋 Pre-Deployment Checklist

### ✅ Development Complete
- [x] All pages created and functional
- [x] UI/UX design implemented
- [x] Responsive design tested
- [x] API endpoints working
- [x] File upload functionality
- [x] Documentation complete

### 🔧 Configuration

#### 1. Update Secret Key
```python
# In app.py, line 7
app.secret_key = 'your-secret-key-change-in-production'
```

**Action Required**:
- [ ] Generate a strong random secret key
- [ ] Update the secret key in app.py
- [ ] Never commit the real key to version control

**Generate Secret Key**:
```python
import secrets
print(secrets.token_hex(32))
```

#### 2. Environment Variables
Create a `.env` file:
```bash
FLASK_ENV=production
SECRET_KEY=your-generated-secret-key
UPLOAD_FOLDER=static/uploads/ads
MAX_CONTENT_LENGTH=52428800
DATABASE_URL=your-database-url
```

**Action Required**:
- [ ] Create .env file
- [ ] Set all environment variables
- [ ] Add .env to .gitignore

#### 3. Database Configuration
Currently using mock data. For production:

**Action Required**:
- [ ] Choose database (SQLite, PostgreSQL, MySQL)
- [ ] Install database driver
- [ ] Create database schema
- [ ] Update app.py with database connection
- [ ] Migrate mock data to database

**Example (SQLite)**:
```python
from flask_sqlalchemy import SQLAlchemy

app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///kiosk.db'
db = SQLAlchemy(app)
```

### 🖨️ Hardware Setup

#### Printer Configuration
**Action Required**:
- [ ] Connect label printer
- [ ] Install printer drivers
- [ ] Test print functionality
- [ ] Configure printer in Settings page
- [ ] Set default badge size
- [ ] Test auto-print feature

**Supported Printers**:
- Zebra ZD420
- DYMO LabelWriter 450
- Brother QL-820NWB

#### Scanner Configuration
**Action Required**:
- [ ] Connect QR code scanner
- [ ] Configure as keyboard wedge
- [ ] Test scanning in Notepad
- [ ] Test scanning in kiosk view
- [ ] Configure scan timeout
- [ ] Enable beep on scan

**Supported Scanners**:
- USB barcode scanners
- Camera-based scanners
- Bluetooth scanners

### 🌐 Network Setup

#### Server Configuration
**Action Required**:
- [ ] Choose hosting provider
- [ ] Set up server (Linux/Windows)
- [ ] Install Python 3.x
- [ ] Install dependencies
- [ ] Configure firewall
- [ ] Set up SSL/TLS certificate

#### Domain & DNS
**Action Required**:
- [ ] Register domain name
- [ ] Configure DNS records
- [ ] Point domain to server
- [ ] Set up SSL certificate
- [ ] Test domain access

### 🔒 Security

#### Application Security
**Action Required**:
- [ ] Update secret key
- [ ] Enable HTTPS/SSL
- [ ] Configure CORS if needed
- [ ] Set up authentication (if required)
- [ ] Implement rate limiting
- [ ] Enable security headers
- [ ] Set up firewall rules

#### File Upload Security
**Action Required**:
- [ ] Verify file type validation
- [ ] Set file size limits
- [ ] Scan uploads for malware
- [ ] Set proper file permissions
- [ ] Regular cleanup of old files

### 📦 Production Server

#### Option 1: Waitress (Windows)
```bash
pip install waitress
waitress-serve --host=0.0.0.0 --port=80 app:app
```

**Action Required**:
- [ ] Install Waitress
- [ ] Test server startup
- [ ] Configure as Windows service
- [ ] Set up auto-restart

#### Option 2: Gunicorn (Linux)
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:80 app:app
```

**Action Required**:
- [ ] Install Gunicorn
- [ ] Configure workers
- [ ] Set up systemd service
- [ ] Enable auto-restart

#### Option 3: Docker
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

**Action Required**:
- [ ] Create Dockerfile
- [ ] Build Docker image
- [ ] Test container
- [ ] Deploy to production

### 🎨 Customization

#### Branding
**Action Required**:
- [ ] Replace logo placeholders
- [ ] Update event name
- [ ] Update event dates
- [ ] Update location
- [ ] Customize welcome message
- [ ] Update footer text

**Files to Update**:
- `templates/kiosk_horizontal.html` (line ~90)
- `templates/kiosk_vertical.html` (line ~140)
- Settings page (via UI)

#### Colors
**Action Required**:
- [ ] Choose brand colors
- [ ] Update CSS variables
- [ ] Test color contrast
- [ ] Update gradient colors

**File to Edit**: `static/mycss.css`

### 📊 Data & Analytics

#### Database Setup
**Action Required**:
- [ ] Create participants table
- [ ] Create scans table
- [ ] Create ads table
- [ ] Create settings table
- [ ] Set up indexes
- [ ] Configure backups

#### Analytics Integration
**Action Required**:
- [ ] Choose analytics platform
- [ ] Add tracking code
- [ ] Set up goals
- [ ] Configure dashboards
- [ ] Test tracking

### 🧪 Testing

#### Functional Testing
**Action Required**:
- [ ] Test all pages load
- [ ] Test navigation
- [ ] Test file upload
- [ ] Test QR scanning
- [ ] Test badge printing
- [ ] Test ad rotation
- [ ] Test settings save
- [ ] Test API endpoints

#### Browser Testing
**Action Required**:
- [ ] Test in Chrome
- [ ] Test in Firefox
- [ ] Test in Safari
- [ ] Test in Edge
- [ ] Test on mobile
- [ ] Test on tablet

#### Performance Testing
**Action Required**:
- [ ] Load test with multiple users
- [ ] Test file upload limits
- [ ] Test ad rotation performance
- [ ] Test database queries
- [ ] Optimize slow pages

### 📱 Kiosk Setup

#### Display Configuration
**Action Required**:
- [ ] Choose monitor orientation
- [ ] Set screen resolution
- [ ] Configure fullscreen mode
- [ ] Disable screensaver
- [ ] Set auto-login
- [ ] Configure auto-start

#### Kiosk Mode
**Windows**:
```
"C:\Program Files\Google\Chrome\Application\chrome.exe" --kiosk --app=http://localhost:5000/kiosk/horizontal
```

**Action Required**:
- [ ] Create startup script
- [ ] Add to Startup folder
- [ ] Test auto-start
- [ ] Configure browser settings
- [ ] Disable browser updates during event

### 🔄 Backup & Recovery

#### Backup Strategy
**Action Required**:
- [ ] Set up database backups
- [ ] Backup uploaded files
- [ ] Backup configuration
- [ ] Test restore process
- [ ] Schedule automatic backups

#### Disaster Recovery
**Action Required**:
- [ ] Document recovery steps
- [ ] Create backup server
- [ ] Test failover
- [ ] Prepare offline mode

### 📝 Documentation

#### User Documentation
**Action Required**:
- [ ] Create user manual
- [ ] Create admin guide
- [ ] Create troubleshooting guide
- [ ] Create video tutorials
- [ ] Print quick reference cards

#### Technical Documentation
**Action Required**:
- [ ] Document API endpoints
- [ ] Document database schema
- [ ] Document deployment process
- [ ] Document maintenance tasks
- [ ] Create runbook

### 👥 Training

#### Staff Training
**Action Required**:
- [ ] Train administrators
- [ ] Train support staff
- [ ] Train on-site personnel
- [ ] Create training materials
- [ ] Conduct practice sessions

### 🚀 Go-Live

#### Pre-Launch (1 Week Before)
**Action Required**:
- [ ] Final testing
- [ ] Load test data
- [ ] Upload advertisements
- [ ] Configure all settings
- [ ] Test hardware
- [ ] Brief staff

#### Launch Day (Day Of Event)
**Action Required**:
- [ ] Verify server is running
- [ ] Check printer status
- [ ] Check scanner status
- [ ] Test end-to-end flow
- [ ] Monitor for issues
- [ ] Have support ready

#### Post-Launch (After Event)
**Action Required**:
- [ ] Export data
- [ ] Generate reports
- [ ] Backup all data
- [ ] Collect feedback
- [ ] Document issues
- [ ] Plan improvements

### 📊 Monitoring

#### System Monitoring
**Action Required**:
- [ ] Set up uptime monitoring
- [ ] Configure error alerts
- [ ] Monitor disk space
- [ ] Monitor memory usage
- [ ] Monitor network
- [ ] Set up logging

#### Application Monitoring
**Action Required**:
- [ ] Monitor API response times
- [ ] Track error rates
- [ ] Monitor user activity
- [ ] Track scan counts
- [ ] Monitor print queue

### 🛠️ Maintenance

#### Regular Maintenance
**Action Required**:
- [ ] Update dependencies
- [ ] Apply security patches
- [ ] Clean up old files
- [ ] Optimize database
- [ ] Review logs
- [ ] Test backups

#### Support Plan
**Action Required**:
- [ ] Set up support email
- [ ] Create support ticket system
- [ ] Document common issues
- [ ] Prepare support scripts
- [ ] Train support team

---

## 🎯 Quick Deployment Steps

### Minimal Deployment (Development/Testing)
1. Update secret key in app.py
2. Install dependencies: `pip install -r requirements.txt`
3. Run: `python app.py`
4. Access: http://localhost:5000

### Production Deployment (Recommended)
1. Complete all checklist items above
2. Set up production server
3. Configure database
4. Install production WSGI server
5. Set up SSL/TLS
6. Configure domain
7. Test thoroughly
8. Go live!

---

## 📞 Support Contacts

### Technical Support
- Email: <EMAIL>
- Phone: +1-XXX-XXX-XXXX
- Hours: 24/7 during event

### Emergency Contacts
- System Admin: [Name] - [Phone]
- Network Admin: [Name] - [Phone]
- On-site Support: [Name] - [Phone]

---

## ✅ Final Checklist

Before going live, verify:
- [ ] All configuration complete
- [ ] Hardware tested and working
- [ ] Network configured
- [ ] Security measures in place
- [ ] Backups configured
- [ ] Staff trained
- [ ] Documentation ready
- [ ] Support plan active
- [ ] Monitoring enabled
- [ ] Emergency contacts available

---

**Ready to deploy? Good luck with your trade expo! 🚀**

*Last Updated: September 30, 2025*

