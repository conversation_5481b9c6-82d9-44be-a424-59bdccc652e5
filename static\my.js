// Custom JavaScript for Kiosk Management System

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
  // Initialize Bootstrap tooltips
  var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });

  // Initialize theme switcher
  initThemeSwitcher();

  // Add smooth scrolling
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth'
        });
      }
    });
  });
});

// Theme switcher
function initThemeSwitcher() {
  const theme = localStorage.getItem('theme') || 'light';
  document.documentElement.setAttribute('data-bs-theme', theme);

  // Update theme links
  updateThemeLinks(theme);
}

function updateThemeLinks(theme) {
  const darkLinks = document.querySelectorAll('.hide-theme-dark');
  const lightLinks = document.querySelectorAll('.hide-theme-light');

  if (theme === 'dark') {
    darkLinks.forEach(link => link.style.display = 'none');
    lightLinks.forEach(link => link.style.display = 'block');
  } else {
    darkLinks.forEach(link => link.style.display = 'block');
    lightLinks.forEach(link => link.style.display = 'none');
  }
}

// Handle theme change
document.addEventListener('click', function(e) {
  if (e.target.closest('[href="?theme=dark"]')) {
    e.preventDefault();
    setTheme('dark');
  } else if (e.target.closest('[href="?theme=light"]')) {
    e.preventDefault();
    setTheme('light');
  }
});

function setTheme(theme) {
  localStorage.setItem('theme', theme);
  document.documentElement.setAttribute('data-bs-theme', theme);
  updateThemeLinks(theme);
}

// Auto-dismiss alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
  const alerts = document.querySelectorAll('.alert:not(.alert-important)');
  alerts.forEach(alert => {
    setTimeout(() => {
      const bsAlert = new bootstrap.Alert(alert);
      bsAlert.close();
    }, 5000);
  });
});

// Form validation helper
function validateForm(formId) {
  const form = document.getElementById(formId);
  if (!form) return false;

  if (!form.checkValidity()) {
    form.classList.add('was-validated');
    return false;
  }

  return true;
}

// Show loading spinner
function showLoading(buttonElement) {
  const originalText = buttonElement.innerHTML;
  buttonElement.disabled = true;
  buttonElement.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Loading...';

  return function hideLoading() {
    buttonElement.disabled = false;
    buttonElement.innerHTML = originalText;
  };
}

// Confirm dialog helper
function confirmAction(message, callback) {
  if (confirm(message)) {
    callback();
  }
}

// Format file size
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
}

// Format date
function formatDate(date) {
  const options = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };
  return new Date(date).toLocaleDateString('en-US', options);
}

// Copy to clipboard
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(() => {
    showNotification('Copied to clipboard!', 'success');
  }).catch(err => {
    console.error('Failed to copy:', err);
    showNotification('Failed to copy', 'danger');
  });
}

// Show notification
function showNotification(message, type = 'info') {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.setAttribute('role', 'alert');
  alertDiv.innerHTML = `
    <div class="d-flex">
      <div>${message}</div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  `;

  const container = document.querySelector('.container-xl');
  if (container) {
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
      const bsAlert = new bootstrap.Alert(alertDiv);
      bsAlert.close();
    }, 3000);
  }
}

// Debounce function for search inputs
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Export functions for global use
window.kioskApp = {
  validateForm,
  showLoading,
  confirmAction,
  formatFileSize,
  formatDate,
  copyToClipboard,
  showNotification,
  debounce,
  setTheme
};
