import serial
import time
import requests
import json
import os

#load config
def load_config():
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Config file not found. Using default config.")
        return {
            # "scanner_com_port": "COM9",
            # "baud_rate": 9600,
            # "api_url": "http://127.0.0.1/api_famev2/v1/process-vcard",
            # "api_key": "31d051aa-7c55-4dad-91f8-e631c0f4af3a",
            # "fair_code": "MFIO2025"
        }

#Main
def run():
    in_vcard = False
    vcard_lines = []
    ser = None
    last_config = {}

    while True:
        try:
            config = load_config()
            if config != last_config:
                print("🔁 Config changed. Reloading serial port...")
                serial_port = config.get("scanner_com_port")
                baud_rate = config.get("baud_rate")
                if ser and ser.is_open:
                    ser.close()
                ser = serial.Serial(serial_port, baud_rate, timeout=1)
                print(f"✅ Serial port {serial_port} opened")
                last_config = config

            line = ser.readline().decode('utf-8', errors='ignore').strip()
            if not line:
                continue

            if line == "BEGIN:VCARD":
                in_vcard = True
                vcard_lines = [line]

            elif line == "END:VCARD" and in_vcard:
                vcard_lines.append(line)
                json_data = vcard_to_json(vcard_lines)
                test_api(json_data)
                in_vcard = False
                vcard_lines = []

            elif in_vcard:
                vcard_lines.append(line)

        except Exception as e:
            print(f"❌ Error: {e}")
            in_vcard = False
            vcard_lines = []
            time.sleep(1)


def vcard_to_json(lines):
    data = {}
    for line in lines:
        if ':' in line:
            key, value = line.split(':', 1)
            if key not in ["BEGIN", "END"]:
                data[key.lower()] = value.strip()
    return data


def test_api(vcard_data):
    config = load_config()
    # concat with vcar_data
    # api_url = 'http://127.0.0.1/api_famev2/v1/buyers/email/'+vcard_data.get('email')
    api_url = 'http://127.0.0.1/api_famev2/v1/kiosk'
    api_key = config.get("api_key")
    fair_code = config.get("fair_code")

    headers = {
        'x-api-key': api_key,
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    payload = {
        'vbarcode': json.dumps(vcard_data),
        'faircode': fair_code
    }

    try:
        response = requests.post(api_url, headers=headers, data=payload, timeout=10)

        if response.status_code == 200:
            print("API response:", response.json())
        else:
            print(f"API error: {response.status_code} - {response.text}")

    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")

    # try:
    #     response = requests.post(api_url, json=payload, headers=headers, timeout=10)

    #     if response.status_code == 200:
    #         print("API response:", response.json())
    #     else:
    #         print(f"API error: {response.status_code} - {response.text}")

    # except requests.exceptions.RequestException as e:
    #     print(f"Request error: {e}")



def process_vcard(vcard_data):
    api_key = os.getenv('API_KEY')
    headers = {
        'x-api-key': api_key,
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    payload = {
        'vbarcode': vcard_data,
        'faircode': fair_code
    }

    try:
        response = requests.post(api_url, json=payload, headers=headers, timeout=10)

        if response.status_code == 200:
            print("API response:", response.json())
        else:
            print(f"API error: {response.status_code} - {response.text}")

    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")

if __name__ == "__main__":
    run()