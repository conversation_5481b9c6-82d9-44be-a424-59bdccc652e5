/* Custom CSS for Kiosk Management System */

/* Smooth transitions */
* {
  transition: all 0.3s ease;
}

/* Card hover effects */
.card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

/* Button enhancements */
.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Avatar animations */
.avatar {
  transition: transform 0.3s ease;
}

.avatar:hover {
  transform: scale(1.1);
}

/* Status indicators */
.status-dot {
  animation: pulse-status 2s infinite;
}

@keyframes pulse-status {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Loading spinner */
.spinner-border {
  animation: spinner-border 0.75s linear infinite;
}

/* Chart containers */
.chart-sm {
  height: 40px;
  margin: 0 -0.75rem -0.75rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Table enhancements */
.table-vcenter td,
.table-vcenter th {
  vertical-align: middle;
}

/* Badge animations */
.badge {
  transition: all 0.3s ease;
}

.badge:hover {
  transform: scale(1.05);
}

/* Alert animations */
.alert {
  animation: slideInDown 0.5s ease;
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Modal animations */
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
}

/* Progress bar animations */
.progress-bar {
  transition: width 0.6s ease;
}

/* Image responsive */
.img-responsive {
  position: relative;
  overflow: hidden;
}

.img-responsive-16by9::before {
  content: "";
  display: block;
  padding-top: 56.25%;
}

/* Empty state */
.empty {
  padding: 3rem 1rem;
  text-align: center;
}

.empty-img {
  margin-bottom: 2rem;
}

/* Datagrid enhancements */
.datagrid-item {
  padding: 0.75rem 0;
}

/* Custom utilities */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Print styles */
@media print {
  .d-print-none {
    display: none !important;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }

  .page-header {
    padding: 1rem 0;
  }
}
