# Quick Start Guide - Kiosk Management System

## 🚀 Getting Started in 5 Minutes

### Step 1: Start the Application
```bash
# Navigate to project directory
cd e:\Python\kioskproj1

# Activate virtual environment
kiosk1\Scripts\activate

# Run the application
python app.py
```

The application will start on http://localhost:5000

### Step 2: Access the System

Open your browser and navigate to:
- **Main Dashboard**: http://localhost:5000
- **Admin Dashboard**: http://localhost:5000/dashboard
- **Ads Management**: http://localhost:5000/ads
- **Settings**: http://localhost:5000/settings
- **Kiosk (Horizontal)**: http://localhost:5000/kiosk/horizontal
- **Kiosk (Vertical)**: http://localhost:5000/kiosk/vertical

## 📋 Page Overview

### 1. Home Page (/)
**Purpose**: Main landing page with overview and quick actions

**Features**:
- Event information display
- Real-time statistics (scans, badges, system status)
- Quick action buttons
- System features list
- System information panel

**What to do**:
- Review event details
- Check system status
- Use quick action buttons to navigate

### 2. Dashboard (/dashboard)
**Purpose**: Monitor kiosk performance and activity

**Features**:
- Live statistics cards
- System status monitoring
- Recent scans table
- Activity charts
- Auto-refresh every 30 seconds

**What to do**:
- Monitor daily scan activity
- Check printer and scanner status
- Review recent participant scans
- Track system performance

### 3. Ads Management (/ads)
**Purpose**: Upload and manage promotional content

**Features**:
- Upload images and videos
- Visual gallery view
- Preview advertisements
- Delete unwanted ads
- File format information

**What to do**:
1. Click "Upload Ad" button
2. Select image (JPG, PNG, GIF) or video (MP4, WEBM, MOV)
3. Click "Upload"
4. Preview ads by clicking "Preview" button
5. Delete ads by clicking "Delete" button

**Supported Formats**:
- Images: JPG, PNG, GIF
- Videos: MP4, WEBM, MOV
- Max file size: 50MB (recommended)
- Recommended resolution: 1920x1080

### 4. Settings (/settings)
**Purpose**: Configure system preferences

**Settings Categories**:

#### General Settings
- Event name and location
- Event dates
- Welcome message

#### Kiosk Display Settings
- Display orientation (Horizontal/Vertical/Auto)
- Idle timeout
- Ad rotation interval
- Screensaver options
- Sound settings

#### Printer Settings
- Printer selection
- Badge size (4x6, 3x4, 4x3)
- Print quality (Draft, Normal, High)
- Auto-print options
- Test print function

#### Scanner Settings
- Scanner type (USB/Camera/Bluetooth)
- Scan timeout
- Beep on scan
- QR code validation

#### Network & API Settings
- API endpoint configuration
- API key
- Connection timeout
- SSL/TLS settings

**What to do**:
1. Configure event details
2. Set up printer and scanner
3. Adjust display preferences
4. Test printer and scanner
5. Click "Save Changes"

### 5. Kiosk View - Horizontal (/kiosk/horizontal)
**Purpose**: Main kiosk interface for landscape monitors

**Layout**:
- Left side: Event info, QR scanning area
- Right side: Advertisements, system status

**Features**:
- Large QR code scanning interface
- Rotating advertisements
- Real-time scan counter
- Success animations
- System status indicators

**What to do**:
1. Open in fullscreen (F11)
2. Position on landscape monitor
3. Scanner will automatically detect QR codes
4. Watch for success animation after scan
5. Badge prints automatically

**Keyboard Simulation**:
- Type any text and press Enter to simulate a scan
- Useful for testing without a physical scanner

### 6. Kiosk View - Vertical (/kiosk/vertical)
**Purpose**: Kiosk interface for portrait monitors

**Layout**:
- Top: Event header
- Middle: Advertisements
- Center: QR scanning area
- Bottom: System status footer

**Features**:
- Optimized for portrait displays
- Compact layout
- All essential information visible
- Touch-friendly interface

**What to do**:
1. Open in fullscreen (F11)
2. Position on portrait monitor
3. Same scanning functionality as horizontal view

## 🎯 Common Tasks

### Task 1: Upload Advertisements
1. Go to **Ads Management** (/ads)
2. Click **"Upload Ad"** button
3. Select your image or video file
4. Click **"Upload"**
5. Ad will appear in gallery and start rotating on kiosk screens

### Task 2: Configure Event Details
1. Go to **Settings** (/settings)
2. Update **Event Name**, **Location**, and **Dates**
3. Customize **Welcome Message**
4. Click **"Save Changes"**

### Task 3: Test the Kiosk
1. Go to **Kiosk View** (horizontal or vertical)
2. Press **F11** for fullscreen
3. Type any text and press **Enter** to simulate scan
4. Watch for success animation
5. Check that counter increments

### Task 4: Monitor Activity
1. Go to **Dashboard** (/dashboard)
2. View real-time statistics
3. Check recent scans table
4. Monitor system status
5. Page auto-refreshes every 30 seconds

### Task 5: Setup Printer
1. Go to **Settings** (/settings)
2. Scroll to **Printer Settings**
3. Select your printer from dropdown
4. Choose badge size and quality
5. Click **"Test Print"** to verify
6. Enable **"Auto-print on Scan"**
7. Click **"Save Changes"**

## 🔧 Testing Without Hardware

### Test QR Scanning (No Scanner)
The kiosk views accept keyboard input:
1. Open kiosk view
2. Type any text (simulates QR code)
3. Press **Enter**
4. Success animation will play
5. Counter will increment

### Test Ads (No Files)
Default placeholder ads are shown:
1. Kiosk displays "Your Ad Here" placeholder
2. Upload real ads to replace placeholder
3. Ads rotate every 10 seconds

### Test Printing (No Printer)
1. Scan simulation triggers print queue
2. Check console for print commands
3. Status shows "Printing..." message
4. Real printer integration needed for actual printing

## 🎨 Customization

### Change Theme
- Click moon icon (🌙) in navbar for dark mode
- Click sun icon (☀️) for light mode
- Theme preference is saved in browser

### Adjust Colors
Edit `static/mycss.css`:
```css
/* Change primary color */
.bg-gradient-primary {
  background: linear-gradient(135deg, #YOUR_COLOR_1 0%, #YOUR_COLOR_2 100%);
}
```

### Change Logo
Replace placeholder logo URLs in templates:
- `templates/kiosk_horizontal.html` (line ~90)
- `templates/kiosk_vertical.html` (line ~140)

## 📱 Fullscreen Kiosk Mode

### Windows
1. Open kiosk view in browser
2. Press **F11** for fullscreen
3. Press **F11** again to exit

### Auto-start on Boot (Windows)
1. Create shortcut to Chrome/Edge
2. Add flags: `--kiosk --app=http://localhost:5000/kiosk/horizontal`
3. Place in Startup folder

Example:
```
"C:\Program Files\Google\Chrome\Application\chrome.exe" --kiosk --app=http://localhost:5000/kiosk/horizontal
```

## 🔍 Troubleshooting

### Application won't start
```bash
# Check if Flask is installed
pip list | findstr Flask

# Install if missing
pip install flask
```

### Page not loading
- Check if app is running (look for "Running on http://...")
- Try http://127.0.0.1:5000 instead of localhost
- Clear browser cache (Ctrl+Shift+Delete)

### Ads not showing
- Check file format (JPG, PNG, GIF, MP4, WEBM, MOV)
- Verify file uploaded successfully
- Check browser console (F12) for errors
- Refresh page (Ctrl+F5)

### Scanner not working
- Ensure scanner is in keyboard wedge mode
- Test scanner in Notepad (should type characters)
- Check USB connection
- Try different USB port

## 📞 Support

### Check Logs
- Browser console: Press **F12** → Console tab
- Flask console: Check terminal where app is running

### Common Error Messages
- "File not found": Check file path and permissions
- "Connection refused": Ensure app is running
- "Upload failed": Check file size and format

## 🎓 Next Steps

1. **Customize Event Details**: Update settings with your event info
2. **Upload Ads**: Add promotional content
3. **Test Scanning**: Simulate scans to verify workflow
4. **Setup Hardware**: Connect printer and scanner
5. **Deploy**: Move to production server for live event

## 📚 Additional Resources

- Full documentation: See `README.md`
- API documentation: Check `app.py` for endpoint details
- Tabler.io docs: https://tabler.io/docs
- Flask docs: https://flask.palletsprojects.com/

---

**Ready to go live?** 🚀

1. Update event details in Settings
2. Upload your advertisements
3. Connect printer and scanner
4. Open kiosk view in fullscreen
5. Start scanning!

