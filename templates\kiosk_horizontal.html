<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>Kiosk - {{ expo_info.name }}</title>

    <!-- CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta20/dist/css/tabler.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/tabler-icons.min.css" rel="stylesheet"/>

    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
        --tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
      }
      body {
        font-feature-settings: "cv03", "cv04", "cv11";
        margin: 0;
        padding: 0;
        overflow: hidden;
        background: #f5f5f5;
      }

      .kiosk-container {
        display: grid;
        grid-template-rows: auto 1fr auto;
        grid-template-columns: 1fr 380px;
        height: 100vh;
        width: 100vw;
        gap: 0;
      }

      /* Header - Full Width */
      .header-section {
        grid-column: 1 / -1;
        background: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header-image {
        width: 100%;
        height: 220px;
        object-fit: cover;
        display: block;
      }

      /* Main Content Area */
      .content-section {
        display: grid;
        grid-template-rows: 1.2fr 1fr;
        gap: 1.5rem;
        padding: 1.5rem;
        background: #f5f5f5;
      }

      /* Advertisement Section */
      .ads-section {
        background: #000;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        position: relative;
        min-height: 400px;
      }

      .ad-container {
        width: 100%;
        height: 100%;
        position: relative;
      }

      .ad-slide {
        display: none;
        width: 100%;
        height: 100%;
        object-fit: contain;
        background: #000;
      }

      .ad-slide.active {
        display: block;
        animation: fadeIn 0.5s ease-in;
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      .ad-badge {
        position: absolute;
        bottom: 1rem;
        right: 1rem;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(10px);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      /* Event Schedule Section */
      .schedule-section {
        background: white;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        position: relative;
        min-height: 350px;
      }

      .schedule-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      .schedule-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        text-align: center;
        padding: 2rem;
      }

      .schedule-header {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
        padding: 1rem 1.5rem;
        color: white;
        font-weight: bold;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        z-index: 1;
      }

      .schedule-header i {
        font-size: 1.5rem;
      }

      /* Sidebar - QR Scanning Info */
      .sidebar-section {
        grid-row: 2 / 3;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative;
      }

      .scan-info-section {
        flex: 1;
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        text-align: center;
        color: white;
        position: relative;
      }

      .scan-header {
        width: 100%;
      }

      .scan-icon {
        font-size: 64px;
        margin-bottom: 0.75rem;
        animation: pulse-icon 2s infinite;
      }

      @keyframes pulse-icon {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.05); opacity: 0.85; }
      }

      .scan-title {
        font-size: 1.75rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        line-height: 1.2;
      }

      .scan-instruction {
        font-size: 1rem;
        margin-bottom: 0;
        opacity: 0.95;
        line-height: 1.4;
      }

      .scan-main {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
      }

      .qr-scanner-box {
        width: 180px;
        height: 180px;
        border: 3px dashed rgba(255, 255, 255, 0.8);
        border-radius: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: pulse-border 2s infinite;
        background: rgba(255, 255, 255, 0.1);
      }

      @keyframes pulse-border {
        0%, 100% {
          border-color: rgba(255, 255, 255, 0.8);
          transform: scale(1);
        }
        50% {
          border-color: rgba(255, 255, 255, 0.5);
          transform: scale(1.02);
        }
      }

      .scan-footer {
        width: 100%;
        padding-top: 1rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
      }

      .scan-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
      }

      .stat-item {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        padding: 0.75rem;
        border-radius: 0.75rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        display: block;
        margin-bottom: 0.25rem;
      }

      .stat-label {
        font-size: 0.75rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .scan-counter {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(10px);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 1.5rem;
        font-weight: 600;
        font-size: 0.875rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .scan-help {
        font-size: 0.85rem;
        opacity: 0.9;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
      }

      /* Success Animation */
      .success-animation {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 10;
        background: rgba(255, 255, 255, 0.98);
        padding: 2.5rem;
        border-radius: 1rem;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
        max-width: 90%;
        max-height: 90%;
        overflow-y: auto;
      }

      .success-animation.show {
        display: block;
        animation: fadeInScale 0.5s ease-out;
      }

      @keyframes fadeInScale {
        0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
      }

      .success-icon {
        font-size: 80px;
        color: #2fb344;
        margin-bottom: 1rem;
      }

      .success-title {
        font-size: 1.75rem;
        font-weight: bold;
        color: #2fb344;
        margin-bottom: 0.5rem;
      }

      .success-message {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 1.5rem;
      }

      .buyer-info {
        background: #f8f9fa;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-top: 1rem;
        text-align: left;
      }

      .buyer-info-row {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid #dee2e6;
      }

      .buyer-info-row:last-child {
        border-bottom: none;
      }

      .buyer-info-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.95rem;
      }

      .buyer-info-value {
        color: #212529;
        font-size: 0.95rem;
        text-align: right;
        max-width: 60%;
        word-wrap: break-word;
      }

      .error-animation {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 10;
        background: rgba(255, 255, 255, 0.98);
        padding: 2.5rem;
        border-radius: 1rem;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
        max-width: 90%;
      }

      .error-animation.show {
        display: block;
        animation: fadeInScale 0.5s ease-out;
      }

      .error-icon {
        font-size: 80px;
        color: #dc3545;
        margin-bottom: 1rem;
      }

      .error-title {
        font-size: 1.75rem;
        font-weight: bold;
        color: #dc3545;
        margin-bottom: 0.5rem;
      }

      .error-message {
        font-size: 1.1rem;
        color: #666;
      }

      /* Footer - Full Width */
      .footer-section {
        grid-column: 1 / -1;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        padding: 1.25rem 2rem;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .footer-info {
        display: flex;
        gap: 2.5rem;
        align-items: center;
      }

      .footer-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.95rem;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
      }

      .footer-item i {
        font-size: 1.25rem;
        color: #3498db;
      }

      .footer-item strong {
        color: white;
      }

      .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        background: rgba(46, 204, 113, 0.2);
        color: #2ecc71;
        border: 1px solid rgba(46, 204, 113, 0.3);
      }

      .pulse-dot {
        width: 8px;
        height: 8px;
        background: #2ecc71;
        border-radius: 50%;
        animation: pulse-dot 2s infinite;
      }

      @keyframes pulse-dot {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.6; transform: scale(1.2); }
      }

      .footer-logo {
        font-size: 1.1rem;
        font-weight: bold;
        color: white;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .footer-logo i {
        font-size: 1.5rem;
        color: #3498db;
      }
    </style>
  </head>
  <body>
    <div class="kiosk-container">
      <!-- Header Section - Full Width -->
      <div class="header-section">
        <!-- Replace this URL with your actual event header image -->
        <img src="https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=1920&h=180&fit=crop"
             alt="Event Header"
             class="header-image"
             id="header-image">
      </div>

      <!-- Main Content Section -->
      <div class="content-section">
        <!-- Advertisement Section (Top 2/3) -->
        <div class="ads-section">
          <div class="ad-container" id="ad-container">
            <!-- Default placeholder if no ads -->
            <div class="ad-slide active" style="display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
              <div class="text-center text-white p-4">
                <i class="ti ti-ad icon" style="font-size: 80px;"></i>
                <h3 class="mt-3">Advertisement Space</h3>
                <p class="mb-0">Upload ads in the management panel</p>
              </div>
            </div>
          </div>
          <div class="ad-badge">
            <i class="ti ti-ad-2 icon me-1"></i>
            Sponsored Content
          </div>
        </div>

        <!-- Event Schedule Section (Bottom 1/3) -->
        <div class="schedule-section">
          <div class="schedule-header">
            <i class="ti ti-calendar-event"></i>
            <span>Event Schedule & Program</span>
          </div>

          <!-- Replace this URL with your actual event schedule image -->
          <img src="https://images.unsplash.com/photo-1506784983877-45594efa4cbe?w=800&h=300&fit=crop"
               alt="Event Schedule"
               class="schedule-image"
               id="schedule-image"
               style="display: none;">

          <!-- Placeholder when no schedule image is uploaded -->
          <div class="schedule-placeholder" id="schedule-placeholder">
            <div>
              <i class="ti ti-calendar-event icon" style="font-size: 60px; margin-bottom: 1rem;"></i>
              <h3>Event Schedule</h3>
              <p class="mb-0">Upload schedule image in settings</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar Section - QR Scanning Info -->
      <div class="sidebar-section">
        <div class="scan-info-section">
          <!-- Scan Counter Badge -->
          <!-- <div class="scan-counter">
            <i class="ti ti-users icon me-1"></i>
            <span id="scan-count">1247</span>
          </div> -->

          <!-- Scanning Interface -->
          <div id="idle-state" style="display: flex; flex-direction: column; height: 100%; width: 100%; justify-content: space-between;">
            <!-- Header -->
            <div class="scan-header">
              <div class="scan-icon">
                <i class="ti ti-qrcode"></i>
              </div>
              <div class="scan-title">Welcome!</div>
              <div class="scan-instruction">Scan your vCard QR code to print your badge</div>
            </div>

            <!-- Main Scanner Box -->
            <div class="scan-main">
              <div class="qr-scanner-box">
                <i class="ti ti-scan icon" style="font-size: 56px; opacity: 0.95;"></i>
              </div>
            </div>

            <!-- Footer Stats -->
            <div class="scan-footer">
              <!-- <div class="scan-stats">
                <div class="stat-item">
                  <span class="stat-value">
                    <i class="ti ti-printer icon"></i>
                  </span>
                  <span class="stat-label">Printer Ready</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">
                    <i class="ti ti-qrcode icon"></i>
                  </span>
                  <span class="stat-label">Scanner Online</span>
                </div>
              </div> -->
              <div class="scan-help">
                <i class="ti ti-info-circle icon"></i>
                <span>Position QR code in front of scanner</span>
              </div>
            </div>
          </div>

          <!-- Success Animation with Buyer Info -->
          <div class="success-animation" id="success-state">
            <div class="success-icon">
              <i class="ti ti-circle-check"></i>
            </div>
            <div class="success-title">Participant Found!</div>
            <div class="success-message">Your badge is printing...</div>

            <!-- Buyer Information Display -->
            <div class="buyer-info" id="buyer-info" style="display: none;">
              <h4 style="margin-bottom: 1rem; color: #667eea;">
                <i class="ti ti-user icon me-2"></i>Participant Information
              </h4>
              <div id="buyer-details"></div>
            </div>
          </div>

          <!-- Error Animation -->
          <div class="error-animation" id="error-state">
            <div class="error-icon">
              <i class="ti ti-alert-circle"></i>
            </div>
            <div class="error-title">Not Found</div>
            <div class="error-message" id="error-message">Participant not found in the system.</div>
          </div>
        </div>
      </div>

      <!-- Footer Section - Full Width -->
      <div class="footer-section">
        <div class="footer-logo">
          <i class="ti ti-building-broadcast"></i>
          <span>{{ expo_info.name }}</span>
        </div>

        <div class="footer-info">
          <div class="footer-item">
            <i class="ti ti-calendar"></i>
            <span>{{ expo_info.date }}</span>
          </div>
          <div class="footer-item">
            <i class="ti ti-map-pin"></i>
            <span>{{ expo_info.location }}</span>
          </div>
          <div class="footer-item">
            <i class="ti ti-building"></i>
            <span><strong>{{ expo_info.booth_count }}+</strong> Exhibitors</span>
          </div>
        </div>

        <div class="footer-info">
          <!-- <span class="status-indicator">
            <span class="pulse-dot"></span>
            <i class="ti ti-printer icon me-1"></i>
            Printer
          </span>
          <span class="status-indicator">
            <span class="pulse-dot"></span>
            <i class="ti ti-qrcode icon me-1"></i>
            Scanner
          </span> -->
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta20/dist/js/tabler.min.js"></script>
    <script>



      // Poll for barcode scans from the hardware scanner
      let lastProcessedBarcode = null;

      async function pollBarcode() {
        try {
          const res = await fetch('/api/barcode');
          const data = await res.json();

          // If we have a new barcode that's different from the last one
          if (data.barcode && data.barcode !== lastProcessedBarcode) {
            lastProcessedBarcode = data.barcode;
            console.log('New barcode scanned:', data.barcode);

            // Process the barcode
            await processBarcodeScanned(data.barcode);
            console.log(data);
          }
        } catch (err) {
          console.error('Error fetching barcode:', err);
        }
      }

      // Process scanned vCard - send to API
      async function processBarcodeScanned(vcard) {
        const idleState = document.getElementById('idle-state');
        const successState = document.getElementById('success-state');
        const errorState = document.getElementById('error-state');

        // Hide idle state
        idleState.style.display = 'none';

        try {
          // Send vCard to API endpoint
          const response = await fetch('/api/process-vcard', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ vcard: vcard })
          });

          const data = await response.json();

          if (response.ok && !data.error) {
            // Success - display participant information
            displayBuyerInfo(data);
            successState.classList.add('show');

            // Update counter
            const counter = document.getElementById('scan-count');
            if (counter) {
              counter.textContent = parseInt(counter.textContent || 0) + 1;
            }

            // Hide after 5 seconds
            setTimeout(() => {
              successState.classList.remove('show');
              idleState.style.display = 'flex';
              lastProcessedBarcode = null; // Allow same barcode to be scanned again
            }, 5000);
          } else {
            // Error - participant not found or API error
            const errorMessage = document.getElementById('error-message');
            errorMessage.textContent = data.error || 'Participant not found in the system.';
            errorState.classList.add('show');

            // Hide after 3 seconds
            setTimeout(() => {
              errorState.classList.remove('show');
              idleState.style.display = 'flex';
              lastProcessedBarcode = null; // Allow retry
            }, 3000);
          }
        } catch (error) {
          console.error('Error processing vCard:', error);

          // Show error state
          const errorMessage = document.getElementById('error-message');
          errorMessage.textContent = 'Error connecting to the system. Please try again.';
          errorState.classList.add('show');

          setTimeout(() => {
            errorState.classList.remove('show');
            idleState.style.display = 'flex';
            lastProcessedBarcode = null;
          }, 3000);
        }
      }

      // Display buyer information in the success dialog
      function displayBuyerInfo(data) {
        const buyerInfo = document.getElementById('buyer-info');
        const buyerDetails = document.getElementById('buyer-details');

        if (!data) {
          buyerInfo.style.display = 'none';
          return;
        }

        // Build the buyer information HTML
        let html = '';

        // Common fields to display (adjust based on your API response structure)
        const fields = [
          { key: 'name', label: 'Name', icon: 'user' },
          { key: 'email', label: 'Email', icon: 'mail' },
          { key: 'company', label: 'Company', icon: 'building' },
          { key: 'position', label: 'Position', icon: 'briefcase' },
          { key: 'phone', label: 'Phone', icon: 'phone' },
          { key: 'country', label: 'Country', icon: 'world' },
          { key: 'badge_type', label: 'Badge Type', icon: 'id-badge' },
        ];

        fields.forEach(field => {
          const value = data[field.key] || data.data?.[field.key];
          if (value) {
            html += `
              <div class="buyer-info-row">
                <span class="buyer-info-label">
                  <i class="ti ti-${field.icon} icon me-2"></i>${field.label}
                </span>
                <span class="buyer-info-value">${value}</span>
              </div>
            `;
          }
        });

        if (html) {
          buyerDetails.innerHTML = html;
          buyerInfo.style.display = 'block';
        } else {
          buyerInfo.style.display = 'none';
        }
      }

      // Start polling for barcodes every 500ms
      setInterval(pollBarcode, 3000);

      // Load and rotate ads
      let currentAdIndex = 0;
      let ads = [];

      async function loadAds() {
        try {
          const response = await fetch('/api/ads');
          ads = await response.json();

          if (ads.length > 0) {
            displayAds();
            setInterval(rotateAds, 10000); // Rotate every 10 seconds
          }
        } catch (error) {
          console.error('Error loading ads:', error);
        }
      }

      function displayAds() {
        const container = document.getElementById('ad-container');
        container.innerHTML = '';

        ads.forEach((ad, index) => {
          let element;
          if (ad.type === 'image') {
            element = document.createElement('img');
            element.src = ad.url;
            element.className = 'ad-slide';
          } else {
            element = document.createElement('video');
            element.src = ad.url;
            element.className = 'ad-slide';
            element.muted = true;
            element.loop = true;
          }

          if (index === 0) {
            element.classList.add('active');
            if (ad.type === 'video') element.play();
          }

          container.appendChild(element);
        });
      }

      function rotateAds() {
        if (ads.length === 0) return;

        const slides = document.querySelectorAll('.ad-slide');
        slides[currentAdIndex].classList.remove('active');

        if (slides[currentAdIndex].tagName === 'VIDEO') {
          slides[currentAdIndex].pause();
        }

        currentAdIndex = (currentAdIndex + 1) % ads.length;
        slides[currentAdIndex].classList.add('active');

        if (slides[currentAdIndex].tagName === 'VIDEO') {
          slides[currentAdIndex].play();
        }
      }

      // Load custom header and schedule images
      function loadCustomImages() {
        // Check if custom header image exists
        const headerImg = new Image();
        headerImg.onload = function() {
          document.getElementById('header-image').src = this.src;
        };
        // Try to load custom header (you can set this via settings)
        headerImg.src = '/static/uploads/header.jpg';

        // Check if custom schedule image exists
        const scheduleImg = new Image();
        scheduleImg.onload = function() {
          document.getElementById('schedule-image').style.display = 'block';
          document.getElementById('schedule-placeholder').style.display = 'none';
          document.getElementById('schedule-image').src = this.src;
        };
        scheduleImg.onerror = function() {
          // Keep placeholder visible if no custom schedule
          document.getElementById('schedule-image').style.display = 'none';
          document.getElementById('schedule-placeholder').style.display = 'flex';
        };
        // Try to load custom schedule (you can set this via settings)
        scheduleImg.src = '/static/uploads/schedule.jpg';
      }



      // Listen for keyboard input (for testing without hardware scanner)
      // Type an email and press Enter to simulate a scan
      let scanBuffer = '';
      let scanTimeout;

      document.addEventListener('keypress', (e) => {
        clearTimeout(scanTimeout);

        if (e.key === 'Enter') {
          if (scanBuffer.length > 0) {
            console.log('Keyboard simulation - scanning:', scanBuffer);
            processBarcodeScanned(scanBuffer);
            scanBuffer = '';
          }
        } else {
          scanBuffer += e.key;
          scanTimeout = setTimeout(() => {
            scanBuffer = '';
          }, 100);
        }
      });

      // Initialize
      loadAds();
      loadCustomImages();

      // Auto-refresh stats every 30 seconds
      // setInterval(async () => {
      //   try {
      //     const response = await fetch('/api/stats');
      //     const stats = await response.json();
      //     document.getElementById('scan-count').textContent = stats.today_scans;
      //   } catch (error) {
      //     console.error('Error updating stats:', error);
      //   }
      // }, 30000);
    </script>
  </body>
</html>

