{% extends "base.html" %}

{% block title %}Ads Management - Kiosk Management System{% endblock %}

{% block page_header %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          Ads Management
        </h2>
        <div class="text-muted mt-1">Manage promotional content for kiosk displays</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-upload">
            <i class="ti ti-upload icon"></i>
            Upload Ad
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block content %}
<div class="row row-deck row-cards">
  <!-- Statistics -->
  <div class="col-sm-6 col-lg-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="subheader">Total Ads</div>
        </div>
        <div class="h1 mb-0">{{ ads|length }}</div>
        <div class="text-muted mt-2">
          <i class="ti ti-photo icon me-1"></i> Images & Videos
        </div>
      </div>
    </div>
  </div>

  <div class="col-sm-6 col-lg-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="subheader">Active Ads</div>
        </div>
        <div class="h1 mb-0">{{ ads|length }}</div>
        <div class="text-muted mt-2">
          <span class="badge bg-green-lt">Running</span>
        </div>
      </div>
    </div>
  </div>

  <div class="col-sm-6 col-lg-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="subheader">Total Size</div>
        </div>
        <div class="h1 mb-0">{{ (ads|sum(attribute='size') / 1024 / 1024)|round(1) }} MB</div>
        <div class="text-muted mt-2">
          <i class="ti ti-database icon me-1"></i> Storage Used
        </div>
      </div>
    </div>
  </div>

  <div class="col-sm-6 col-lg-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="subheader">Rotation Time</div>
        </div>
        <div class="h1 mb-0">10s</div>
        <div class="text-muted mt-2">
          <i class="ti ti-clock icon me-1"></i> Per Ad
        </div>
      </div>
    </div>
  </div>

  <!-- Ads Gallery -->
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Ad Gallery</h3>
        <div class="ms-auto">
          <div class="btn-group">
            <button class="btn btn-sm btn-outline-primary active">
              <i class="ti ti-layout-grid icon"></i> Grid
            </button>
            <button class="btn btn-sm btn-outline-primary">
              <i class="ti ti-list icon"></i> List
            </button>
          </div>
        </div>
      </div>
      <div class="card-body">
        {% if ads|length > 0 %}
        <div class="row row-cards">
          {% for ad in ads %}
          <div class="col-sm-6 col-lg-4">
            <div class="card card-sm">
              <div class="card-img-top img-responsive img-responsive-16by9" style="background-color: #f8f9fa; position: relative; min-height: 200px;">
                {% if ad.type == 'image' %}
                  <img src="{{ ad.url }}" alt="{{ ad.filename }}" style="width: 100%; height: 100%; object-fit: cover; position: absolute; top: 0; left: 0;">
                {% else %}
                  <video src="{{ ad.url }}" style="width: 100%; height: 100%; object-fit: cover; position: absolute; top: 0; left: 0;" muted></video>
                  <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1;">
                    <i class="ti ti-player-play icon" style="font-size: 48px; color: white; text-shadow: 0 2px 4px rgba(0,0,0,0.5);"></i>
                  </div>
                {% endif %}
              </div>
              <div class="card-body">
                <div class="d-flex align-items-center mb-2">
                  <div class="me-auto">
                    <h3 class="card-title mb-0">{{ ad.filename }}</h3>
                  </div>
                  <div class="ms-auto">
                    {% if ad.type == 'image' %}
                      <span class="badge bg-blue-lt">
                        <i class="ti ti-photo icon"></i> Image
                      </span>
                    {% else %}
                      <span class="badge bg-purple-lt">
                        <i class="ti ti-video icon"></i> Video
                      </span>
                    {% endif %}
                  </div>
                </div>
                <div class="text-muted small mb-3">
                  Size: {{ (ad.size / 1024)|round(1) }} KB
                </div>
                <div class="btn-list">
                  <button class="btn btn-sm btn-outline-primary" onclick="previewAd('{{ ad.url }}', '{{ ad.type }}')">
                    <i class="ti ti-eye icon"></i> Preview
                  </button>
                  <form method="POST" action="{{ url_for('delete_ad', filename=ad.filename) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this ad?');">
                    <button type="submit" class="btn btn-sm btn-outline-danger">
                      <i class="ti ti-trash icon"></i> Delete
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
        {% else %}
        <div class="empty">
          <div class="empty-img"><img src="https://tabler.io/static/illustrations/undraw_no_data_qbuo.svg" height="128" alt="">
          </div>
          <p class="empty-title">No ads uploaded yet</p>
          <p class="empty-subtitle text-muted">
            Upload images or videos to display on your kiosk screens
          </p>
          <div class="empty-action">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-upload">
              <i class="ti ti-upload icon"></i>
              Upload Your First Ad
            </button>
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Supported Formats -->
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Supported Formats</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <h4 class="mb-3">Images</h4>
            <div class="list-group list-group-flush">
              <div class="list-group-item">
                <div class="row align-items-center">
                  <div class="col-auto">
                    <span class="avatar bg-blue-lt">
                      <i class="ti ti-file-type-jpg icon"></i>
                    </span>
                  </div>
                  <div class="col">
                    <div class="font-weight-medium">JPEG / JPG</div>
                    <div class="text-muted small">Recommended for photos</div>
                  </div>
                </div>
              </div>
              <div class="list-group-item">
                <div class="row align-items-center">
                  <div class="col-auto">
                    <span class="avatar bg-green-lt">
                      <i class="ti ti-file-type-png icon"></i>
                    </span>
                  </div>
                  <div class="col">
                    <div class="font-weight-medium">PNG</div>
                    <div class="text-muted small">Supports transparency</div>
                  </div>
                </div>
              </div>
              <div class="list-group-item">
                <div class="row align-items-center">
                  <div class="col-auto">
                    <span class="avatar bg-purple-lt">
                      <i class="ti ti-file-type-gif icon"></i>
                    </span>
                  </div>
                  <div class="col">
                    <div class="font-weight-medium">GIF</div>
                    <div class="text-muted small">Animated images</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <h4 class="mb-3">Videos</h4>
            <div class="list-group list-group-flush">
              <div class="list-group-item">
                <div class="row align-items-center">
                  <div class="col-auto">
                    <span class="avatar bg-orange-lt">
                      <i class="ti ti-file-type-mp4 icon"></i>
                    </span>
                  </div>
                  <div class="col">
                    <div class="font-weight-medium">MP4</div>
                    <div class="text-muted small">Most compatible format</div>
                  </div>
                </div>
              </div>
              <div class="list-group-item">
                <div class="row align-items-center">
                  <div class="col-auto">
                    <span class="avatar bg-red-lt">
                      <i class="ti ti-file-type-webm icon"></i>
                    </span>
                  </div>
                  <div class="col">
                    <div class="font-weight-medium">WEBM</div>
                    <div class="text-muted small">Web-optimized video</div>
                  </div>
                </div>
              </div>
              <div class="list-group-item">
                <div class="row align-items-center">
                  <div class="col-auto">
                    <span class="avatar bg-yellow-lt">
                      <i class="ti ti-file-type-mov icon"></i>
                    </span>
                  </div>
                  <div class="col">
                    <div class="font-weight-medium">MOV</div>
                    <div class="text-muted small">QuickTime format</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Upload Modal -->
<div class="modal modal-blur fade" id="modal-upload" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Upload Advertisement</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form method="POST" action="{{ url_for('upload_ad') }}" enctype="multipart/form-data">
        <div class="modal-body">
          <div class="mb-3">
            <label class="form-label">Select File</label>
            <input type="file" class="form-control" name="file" accept="image/*,video/*" required>
            <small class="form-hint">Supported formats: JPG, PNG, GIF, MP4, WEBM, MOV</small>
          </div>
          <div class="alert alert-info">
            <i class="ti ti-info-circle icon me-2"></i>
            <strong>Tip:</strong> For best results, use images with 1920x1080 resolution and videos under 50MB.
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">
            <i class="ti ti-upload icon"></i>
            Upload
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Preview Modal -->
<div class="modal modal-blur fade" id="modal-preview" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Ad Preview</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center" id="preview-content">
        <!-- Preview content will be inserted here -->
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  function previewAd(url, type) {
    const previewContent = document.getElementById('preview-content');
    if (type === 'image') {
      previewContent.innerHTML = `<img src="${url}" class="img-fluid" alt="Preview">`;
    } else {
      previewContent.innerHTML = `<video src="${url}" class="img-fluid" controls autoplay></video>`;
    }
    const modal = new bootstrap.Modal(document.getElementById('modal-preview'));
    modal.show();
  }
</script>
{% endblock %}

