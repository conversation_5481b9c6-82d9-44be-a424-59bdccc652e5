# Kiosk Horizontal Layout Guide

## 📐 New Layout Overview

The kiosk horizontal view has been redesigned with a professional grid layout optimized for landscape monitors.

---

## 🎨 Layout Structure

```
┌─────────────────────────────────────────────────────────────────┐
│                      HEADER IMAGE (Full Width)                  │
│                         1920 x 180px                            │
├──────────────────────────────────────┬──────────────────────────┤
│                                      │                          │
│         ADVERTISEMENT AREA           │                          │
│         (Rotating Images/Videos)     │      QR SCANNING         │
│              Large Display           │         INFO             │
│                                      │                          │
│                                      │    • Welcome Message     │
├──────────────────────────────────────┤    • QR Scanner Box      │
│                                      │    • Instructions        │
│       EVENT SCHEDULE IMAGE           │    • Scan Counter        │
│          800 x 300px                 │                          │
│                                      │                          │
├──────────────────────────────────────┴──────────────────────────┤
│                    FOOTER (Full Width)                          │
│   Event Info  •  Location  •  Exhibitors  •  System Status     │
└─────────────────────────────────────────────────────────────────┘
```

---

## 📦 Layout Sections

### 1. **Header Section** (Full Width)
- **Purpose**: Display event branding and header image
- **Dimensions**: 1920px × 180px (recommended)
- **Content**: Event banner, logo, branding
- **Upload**: Settings page → Kiosk Images → Header Image

### 2. **Advertisement Area** (Left Side, Top 2/3)
- **Purpose**: Display rotating advertisements
- **Content**: Images and videos uploaded via Ads Management
- **Rotation**: 10 seconds per ad (configurable)
- **Supported Formats**: 
  - Images: JPG, PNG, GIF
  - Videos: MP4, WEBM, MOV

### 3. **Event Schedule Section** (Left Side, Bottom 1/3)
- **Purpose**: Display event schedule or program
- **Dimensions**: 800px × 300px (recommended)
- **Content**: Schedule image, program, timeline
- **Upload**: Settings page → Kiosk Images → Event Schedule

### 4. **QR Scanning Info** (Right Sidebar)
- **Purpose**: Guide users to scan QR codes
- **Features**:
  - Large QR icon with pulse animation
  - Welcome message
  - Scanning instructions
  - Visual scanner box
  - Scan counter badge
  - Success animation on scan

### 5. **Footer Section** (Full Width)
- **Purpose**: Display event info and system status
- **Content**:
  - Event dates
  - Location
  - Exhibitor count
  - Printer status
  - Scanner status

---

## 🖼️ Image Upload Instructions

### Upload Header Image

1. Go to **Settings** page
2. Scroll to **Kiosk Images** section
3. Under **Header Image**:
   - Click "Choose File"
   - Select your header image (1920×180px recommended)
   - Click "Upload Header"
   - Preview will appear below

**Recommended Specifications:**
- **Size**: 1920px × 180px
- **Format**: JPG or PNG
- **File Size**: < 5MB
- **Content**: Event logo, title, branding

### Upload Schedule Image

1. Go to **Settings** page
2. Scroll to **Kiosk Images** section
3. Under **Event Schedule Image**:
   - Click "Choose File"
   - Select your schedule image (800×300px recommended)
   - Click "Upload Schedule"
   - Preview will appear below

**Recommended Specifications:**
- **Size**: 800px × 300px
- **Format**: JPG or PNG
- **File Size**: < 3MB
- **Content**: Event schedule, program, timeline

---

## 📢 Advertisement Management

### Upload Advertisements

1. Go to **Ads Management** page
2. Click **Upload Ad** button
3. Select image or video file
4. Click **Upload**
5. Ad will appear in gallery and rotation

**Supported Formats:**
- **Images**: JPG, PNG, GIF
- **Videos**: MP4, WEBM, MOV
- **Max Size**: 50MB per file

### Ad Rotation

- Ads rotate automatically every **10 seconds**
- Videos play with sound muted
- Videos loop automatically
- Smooth fade transitions between ads

---

## 🎯 Design Features

### Visual Elements

1. **Gradient Background** (Sidebar)
   - Purple gradient (#667eea → #764ba2)
   - Professional and modern look

2. **Pulse Animations**
   - QR scanner box pulses
   - QR icon animates
   - Status indicators pulse

3. **Success Animation**
   - Large checkmark icon
   - "Success!" message
   - "Badge is printing..." text
   - Auto-dismisses after 3 seconds

4. **Scan Counter Badge**
   - Displays total scans today
   - Updates in real-time
   - Positioned in top-right of sidebar

### Responsive Design

- Optimized for **1920×1080** landscape monitors
- Grid-based layout for perfect alignment
- All sections scale proportionally
- Professional spacing and padding

---

## ⚙️ Configuration

### Customize Event Information

Edit in `app.py` (line ~40):

```python
expo_info = {
    'name': 'International Trade Expo 2025',
    'date': 'September 30 - October 2, 2025',
    'location': 'Convention Center Hall A',
    'booth_count': 250
}
```

### Customize Ad Rotation Speed

Edit in `templates/kiosk_horizontal.html` (line ~470):

```javascript
setInterval(rotateAds, 10000); // Change 10000 to desired milliseconds
```

### Customize Colors

Edit in `templates/kiosk_horizontal.html` (CSS section):

```css
/* Gradient colors */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* Change to your brand colors */
background: linear-gradient(135deg, #YOUR_COLOR_1 0%, #YOUR_COLOR_2 100%);
```

---

## 🚀 Testing the Layout

### View Kiosk in Browser

1. Start the application: `python app.py`
2. Open browser: http://localhost:5000/kiosk/horizontal
3. Press **F11** for fullscreen mode

### Test QR Scanning

**Method 1: Keyboard Simulation**
- Type any text on keyboard
- Press **Enter**
- Success animation will appear

**Method 2: Physical Scanner**
- Connect USB QR scanner
- Configure as keyboard wedge
- Scan any QR code
- Badge printing will trigger

### Test Ad Rotation

1. Upload multiple ads via Ads Management
2. Open kiosk view
3. Ads will rotate every 10 seconds
4. Videos will play automatically

---

## 📱 Fullscreen Mode

### Enable Fullscreen

**Windows:**
- Press **F11** in browser
- Or use Chrome kiosk mode:
  ```
  chrome.exe --kiosk http://localhost:5000/kiosk/horizontal
  ```

**Auto-start on Boot:**
1. Create shortcut to Chrome with kiosk flag
2. Place in Windows Startup folder
3. Set Windows to auto-login

---

## 🎨 Image Creation Tips

### Header Image (1920×180px)

**Design Tips:**
- Use your event logo prominently
- Include event name and dates
- Use brand colors
- Keep text readable from distance
- Export at high quality (90%+)

**Tools:**
- Canva (free templates)
- Adobe Photoshop
- Figma
- GIMP (free)

### Schedule Image (800×300px)

**Design Tips:**
- Use clear, readable fonts
- Organize by time slots
- Use icons for activities
- Color-code different tracks
- Keep layout simple

**Content Ideas:**
- Daily schedule
- Speaker lineup
- Workshop times
- Break times
- Special events

---

## 🔧 Troubleshooting

### Header Image Not Showing

1. Check file exists: `static/uploads/header.jpg`
2. Verify file permissions
3. Clear browser cache (Ctrl+F5)
4. Check file size (< 5MB)
5. Verify image format (JPG/PNG)

### Schedule Image Not Showing

1. Check file exists: `static/uploads/schedule.jpg`
2. Upload via Settings page
3. Refresh kiosk view
4. Check browser console for errors

### Ads Not Rotating

1. Upload ads via Ads Management
2. Check `/api/ads` endpoint returns data
3. Open browser console for errors
4. Verify ad files exist in `static/uploads/ads/`

### Layout Looks Wrong

1. Verify monitor resolution (1920×1080 recommended)
2. Use fullscreen mode (F11)
3. Clear browser cache
4. Check browser zoom is 100%

---

## 📊 File Locations

```
kioskproj1/
├── static/
│   └── uploads/
│       ├── header.jpg          ← Header image
│       ├── schedule.jpg        ← Schedule image
│       └── ads/                ← Advertisement files
│           ├── ad1.jpg
│           ├── ad2.mp4
│           └── ...
├── templates/
│   └── kiosk_horizontal.html   ← Kiosk layout
└── app.py                      ← Flask application
```

---

## ✅ Pre-Event Checklist

- [ ] Upload header image (1920×180px)
- [ ] Upload schedule image (800×300px)
- [ ] Upload all advertisements
- [ ] Test ad rotation
- [ ] Configure event information
- [ ] Test QR scanning
- [ ] Test badge printing
- [ ] Enable fullscreen mode
- [ ] Test on actual kiosk hardware
- [ ] Train staff on system

---

## 🎯 Best Practices

1. **Image Quality**: Use high-resolution images for professional look
2. **File Size**: Optimize images to reduce load times
3. **Testing**: Test on actual hardware before event
4. **Backup**: Keep backup copies of all images
5. **Updates**: Update schedule image daily if needed
6. **Monitoring**: Check system status regularly during event

---

**Need Help?** Check the main README.md or QUICK_START.md for more information.

