# vCard QR Code Integration Guide

## 🎯 Overview

The kiosk system now processes vCard QR codes and sends them to your external API with the event code (faircode) for participant lookup and badge printing.

---

## 🔄 How It Works

### Complete Workflow

```
┌─────────────────────────────────────────────────────────────┐
│ 1. Participant scans vCard QR code                         │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 2. Hardware scanner sends vCard data via COM port          │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 3. Flask app reads from serial port (background thread)    │
│    - Stores in last_barcode variable                       │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 4. Kiosk frontend polls /api/barcode every 3 seconds       │
│    - Detects new vCard data                                │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 5. Frontend sends POST to /api/process-vcard               │
│    Body: { "vcard": "vCard data..." }                      │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 6. Flask backend sends POST to external API                │
│    Body: {                                                  │
│      "vbarcode": "vCard data...",                          │
│      "faircode": "EVENTCODE2025"                           │
│    }                                                        │
│    Headers: { "x-api-key": "..." }                         │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 7. External API returns participant information            │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 8. Display participant info on screen (5 seconds)          │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 9. Print badge automatically                                │
└─────────────────────────────────────────────────────────────┘
```

---

## ⚙️ Configuration

### 1. Set Event Code (Fair Code)

Edit `constants.py`:

```python
# constants.py

KIOSK_ID = 1
APP_NAME = "My Kiosk App"
FAIR_CODE = "EVENTCODE2025"  # ← Change this to your event code
```

**Example Event Codes**:
- `TRADEEXPO2025`
- `FAMEV2_2025`
- `CITEM_EXPO_2025`

### 2. Configure API Endpoint

Edit `app.py` (line ~306):

```python
# Update this URL to your actual API endpoint
api_url = 'http://127.0.0.1/api_famev2/v1/process-vcard'
```

**Change to your actual endpoint**, for example:
- `http://your-server.com/api/v1/process-vcard`
- `http://*************/api/process-participant`
- `https://api.yourcompany.com/v1/scan`

### 3. Configure API Key

Edit `app.py` (line ~308):

```python
headers = {
    'x-api-key': '31d051aa-7c55-4dad-91f8-e631c0f4af3a',  # ← Your API key
    'Accept': 'application/json',
    'Content-Type': 'application/json'
}
```

### 4. Configure COM Port

1. Go to: http://localhost:5000/settings
2. Enter COM port number (e.g., `COM3`)
3. Click "Save Settings"
4. Restart Flask app

---

## 📡 API Request Format

### Endpoint Called by Flask Backend

**Method**: `POST`

**URL**: `http://127.0.0.1/api_famev2/v1/process-vcard` (configurable)

**Headers**:
```json
{
  "x-api-key": "31d051aa-7c55-4dad-91f8-e631c0f4af3a",
  "Accept": "application/json",
  "Content-Type": "application/json"
}
```

**Request Body**:
```json
{
  "vbarcode": "BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nEMAIL:<EMAIL>\nEND:VCARD",
  "faircode": "EVENTCODE2025"
}
```

### Expected API Response

**Success Response** (200 OK):
```json
{
  "success": true,
  "participant": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "company": "ABC Corporation",
    "position": "CEO",
    "phone": "+1234567890",
    "country": "Philippines",
    "badge_type": "VIP"
  }
}
```

**Error Response** (404 Not Found):
```json
{
  "error": "Participant not found",
  "message": "No participant found with this vCard"
}
```

**Error Response** (400 Bad Request):
```json
{
  "error": "Invalid vCard format",
  "message": "The vCard data is malformed"
}
```

---

## 📋 vCard Format

### Standard vCard QR Code Format

```
BEGIN:VCARD
VERSION:3.0
FN:John Doe
N:Doe;John;;;
ORG:ABC Corporation
TITLE:CEO
TEL:+1234567890
EMAIL:<EMAIL>
ADR:;;123 Main St;City;State;12345;Country
END:VCARD
```

### Minimal vCard Format

```
BEGIN:VCARD
VERSION:3.0
FN:John Doe
EMAIL:<EMAIL>
END:VCARD
```

### What the Scanner Sends

The QR scanner will send the entire vCard text as a single string:

```
BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nEMAIL:<EMAIL>\nEND:VCARD
```

---

## 🧪 Testing

### Method 1: Keyboard Simulation

1. **Start Flask app**:
   ```bash
   python app.py
   ```

2. **Open kiosk view**:
   ```
   http://localhost:5000/kiosk/horizontal
   ```

3. **Press F11** for fullscreen

4. **Type a vCard** (or any test string):
   ```
   BEGIN:VCARD
VERSION:3.0
FN:John Doe
EMAIL:<EMAIL>
END:VCARD
   ```

5. **Press Enter**

6. **Check browser console** (F12) to see the request/response

### Method 2: Test API Endpoint Directly

**Test the Flask endpoint**:

```bash
curl -X POST http://localhost:5000/api/process-vcard \
  -H "Content-Type: application/json" \
  -d '{"vcard": "BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nEMAIL:<EMAIL>\nEND:VCARD"}'
```

**Expected Response**:
```json
{
  "success": true,
  "participant": { ... }
}
```

### Method 3: Test with Hardware Scanner

1. Configure COM port in Settings
2. Restart Flask app
3. Scan a vCard QR code
4. Watch console output:
   ```
   Barcode scanned: BEGIN:VCARD...
   ```
5. Kiosk should display participant info

---

## 🔍 Troubleshooting

### Issue 1: "No vCard data provided"

**Cause**: Empty or null vCard data

**Solution**:
1. Check scanner is working
2. Verify QR code contains vCard data
3. Check console: `console.log(data)` in browser
4. Verify `/api/barcode` returns data

### Issue 2: "API error: 404"

**Cause**: External API endpoint not found

**Solution**:
1. Verify API URL is correct in `app.py`
2. Test API endpoint directly with curl
3. Check API server is running
4. Verify network connectivity

### Issue 3: "API error: 401" or "API error: 403"

**Cause**: Invalid or missing API key

**Solution**:
1. Verify API key in `app.py` is correct
2. Check API key has proper permissions
3. Test with curl using the same API key

### Issue 4: Scanner not detecting vCard

**Cause**: COM port not configured or scanner not connected

**Solution**:
1. Check Device Manager for COM port
2. Configure correct COM port in Settings
3. Restart Flask app
4. Check console for "Serial port COM3 opened successfully"

### Issue 5: vCard data truncated

**Cause**: Serial buffer size or timeout issues

**Solution**:
1. Increase serial timeout in `app.py`
2. Check scanner settings (suffix/prefix)
3. Verify scanner baud rate matches (9600)

---

## 🎨 UI Display

### Success State (5 seconds)

When vCard is successfully processed:

- ✅ **Green checkmark icon**
- ✅ **"Participant Found!" title**
- ✅ **Participant information card**:
  - Name
  - Email
  - Company
  - Position
  - Phone
  - Country
  - Badge Type
- ✅ **"Your badge is printing..." message**
- ✅ **Auto-closes after 5 seconds**

### Error State (3 seconds)

When vCard processing fails:

- ❌ **Red alert icon**
- ❌ **"Not Found" title**
- ❌ **Error message** (from API)
- ❌ **Auto-closes after 3 seconds**

---

## 📊 Monitoring

### Check Barcode Polling

Open browser console (F12) and watch for:

```javascript
// Every 3 seconds
console.log(data);  // Shows barcode data from /api/barcode
```

### Check API Calls

In browser Network tab (F12 → Network):

1. **POST to `/api/process-vcard`**
   - Request payload: `{ "vcard": "..." }`
   - Response: Participant data or error

2. **POST to external API**
   - Check Flask console for request details
   - Check response status code

### Flask Console Output

```
Serial port COM3 opened successfully
 * Running on http://0.0.0.0:5000
Barcode scanned: BEGIN:VCARD...
127.0.0.1 - - [01/Oct/2025 10:30:45] "POST /api/process-vcard HTTP/1.1" 200 -
```

---

## 🔐 Security Notes

1. **API Key**: Keep your API key secure
   - Don't commit to version control
   - Use environment variables in production
   - Rotate keys regularly

2. **HTTPS**: Use HTTPS in production
   - Update API URL to `https://`
   - Verify SSL certificates

3. **Input Validation**: vCard data is validated
   - Empty data rejected
   - Malformed data handled gracefully

---

## 📝 Customization

### Change Polling Interval

Edit `kiosk_horizontal.html` (line ~800):

```javascript
setInterval(pollBarcode, 3000);  // 3 seconds (3000ms)
```

Change to:
- `1000` for 1 second (faster, more CPU)
- `5000` for 5 seconds (slower, less CPU)

### Change Display Duration

Edit `kiosk_horizontal.html` (line ~720):

```javascript
setTimeout(() => {
  successState.classList.remove('show');
  idleState.style.display = 'flex';
  lastProcessedBarcode = null;
}, 5000);  // 5 seconds
```

### Customize Display Fields

Edit `kiosk_horizontal.html` (line ~760):

```javascript
const fields = [
  { key: 'name', label: 'Name', icon: 'user' },
  { key: 'email', label: 'Email', icon: 'mail' },
  { key: 'company', label: 'Company', icon: 'building' },
  // Add or remove fields as needed
];
```

---

## ✅ Pre-Event Checklist

- [ ] Set FAIR_CODE in constants.py
- [ ] Configure API endpoint URL
- [ ] Configure API key
- [ ] Configure COM port in Settings
- [ ] Test with keyboard simulation
- [ ] Test with hardware scanner
- [ ] Test with real vCard QR codes
- [ ] Verify API connectivity
- [ ] Test badge printing
- [ ] Train staff on system
- [ ] Prepare backup QR codes

---

## 🚀 Quick Reference

### Files Modified

1. **`constants.py`** - Added FAIR_CODE
2. **`app.py`** - Added `/api/process-vcard` endpoint
3. **`templates/kiosk_horizontal.html`** - Updated to send vCard to API

### Key Variables

- **`FAIR_CODE`** - Event code (in constants.py)
- **`vbarcode`** - vCard data sent to API
- **`faircode`** - Event code sent to API

### Key Endpoints

- **`GET /api/barcode`** - Get last scanned barcode
- **`POST /api/process-vcard`** - Process vCard and call external API

---

**Status**: ✅ **Ready for vCard Processing!**

The system is now configured to handle vCard QR codes and send them to your external API with the event code.

