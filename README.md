# Kiosk Management System

A professional, production-ready kiosk system for trade expo badge printing with QR code scanning, digital advertising, and comprehensive management features.

## Features

### 🏠 Home Page
- Welcome dashboard with event information
- Real-time statistics (scans, badges printed, system status)
- Quick action buttons for easy navigation
- System features overview
- Live system information display

### 📊 Dashboard
- Real-time monitoring of kiosk performance
- Statistics cards with trend indicators
- System status monitoring (Printer, Scanner, Database, Network)
- Recent scans table with participant information
- Auto-refresh every 30 seconds
- Activity charts and analytics

### 🖥️ Kiosk Views

#### Horizontal Monitor (Landscape)
- Optimized for 16:9 landscape displays
- Split-screen layout with main scanning area and sidebar
- Large QR code scanning interface
- Rotating advertisement display
- Real-time scan counter
- System status indicators
- Success animations on successful scans

#### Vertical Monitor (Portrait)
- Optimized for portrait orientation displays
- Stacked layout with ads at top
- Centered scanning interface
- Compact footer with system status
- Perfect for standing kiosks

### 📢 Ads Management
- Upload images (JPG, PNG, GIF) and videos (MP4, WEBM, MOV)
- Visual gallery view of all advertisements
- Preview functionality for ads
- Delete ads with confirmation
- File size and format information
- Supported formats documentation
- Drag-and-drop upload interface

### ⚙️ Settings
- **General Settings**: Event name, location, dates, welcome message
- **Kiosk Display**: Orientation, timeouts, ad rotation, screensaver
- **Printer Settings**: Printer selection, badge size, print quality, auto-print
- **Scanner Settings**: Scanner type, timeout, validation options
- **Network & API**: API endpoint, authentication, SSL/TLS
- **Advanced Settings**: Logging, debug mode, auto-update

## Technology Stack

- **Backend**: Flask (Python)
- **Frontend**: Tabler.io (Bootstrap-based UI framework)
- **Icons**: Tabler Icons
- **Styling**: Custom CSS with animations
- **JavaScript**: Vanilla JS with modern ES6+ features

## Installation

1. **Clone the repository**
   ```bash
   cd e:\Python\kioskproj1
   ```

2. **Activate virtual environment**
   ```bash
   kiosk1\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install flask
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the application**
   - Main application: http://localhost:5000
   - Kiosk (Horizontal): http://localhost:5000/kiosk/horizontal
   - Kiosk (Vertical): http://localhost:5000/kiosk/vertical

## Project Structure

```
kioskproj1/
├── app.py                          # Main Flask application
├── static/
│   ├── mycss.css                   # Custom CSS styles
│   ├── my.js                       # Custom JavaScript
│   └── uploads/
│       └── ads/                    # Uploaded advertisement files
├── templates/
│   ├── base.html                   # Base template with Tabler.io
│   ├── index.html                  # Home page
│   ├── dashboard.html              # Admin dashboard
│   ├── settings.html               # Settings page
│   ├── ads_management.html         # Ads management page
│   ├── kiosk_horizontal.html       # Horizontal kiosk view
│   └── kiosk_vertical.html         # Vertical kiosk view
└── README.md                       # This file
```

## API Endpoints

### Public Endpoints
- `GET /` - Home page
- `GET /dashboard` - Admin dashboard
- `GET /settings` - Settings page
- `GET /ads` - Ads management page
- `GET /kiosk/horizontal` - Horizontal kiosk view
- `GET /kiosk/vertical` - Vertical kiosk view

### API Endpoints
- `POST /api/scan` - Process QR code scan
- `GET /api/ads` - Get list of advertisements
- `GET /api/stats` - Get current statistics
- `POST /ads/upload` - Upload advertisement file
- `POST /ads/delete/<filename>` - Delete advertisement

## Features in Detail

### QR Code Scanning
- Automatic detection via USB scanner or keyboard wedge
- Real-time processing and validation
- Success/error feedback with animations
- Automatic badge printing queue

### Badge Printing
- Support for multiple printer types (Zebra, DYMO, Brother)
- Configurable badge sizes and quality
- Auto-print on successful scan
- Print queue management
- Paper level monitoring

### Advertisement Display
- Automatic rotation of images and videos
- Configurable rotation interval
- Support for multiple formats
- Smooth transitions between ads
- Fallback placeholder when no ads uploaded

### System Monitoring
- Real-time status of printer and scanner
- Network connectivity monitoring
- Database connection status
- Performance metrics and analytics

## Configuration

### Environment Variables
Create a `.env` file for production settings:
```
FLASK_ENV=production
SECRET_KEY=your-secret-key-here
UPLOAD_FOLDER=static/uploads/ads
MAX_CONTENT_LENGTH=52428800  # 50MB max file size
```

### Printer Configuration
Configure your label printer in the settings page:
1. Select printer type
2. Set badge size
3. Configure print quality
4. Test print to verify

### Scanner Configuration
Configure your QR code scanner:
1. Set scanner type (USB/Camera/Bluetooth)
2. Configure scan timeout
3. Enable/disable beep on scan
4. Test scanner functionality

## Production Deployment

### Using Waitress (Windows)
```bash
pip install waitress
waitress-serve --host=0.0.0.0 --port=5000 app:app
```

### Using Gunicorn (Linux)
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Security Recommendations
1. Change the default secret key in `app.py`
2. Enable SSL/TLS for production
3. Set up proper authentication
4. Configure firewall rules
5. Regular security updates
6. Backup database regularly

## Browser Compatibility
- Chrome/Edge (Recommended)
- Firefox
- Safari
- Opera

## Hardware Requirements

### Recommended Setup
- **Computer**: Intel i3 or better, 4GB RAM
- **Monitor**: 1920x1080 (horizontal) or 1080x1920 (vertical)
- **Scanner**: USB QR code scanner or camera
- **Printer**: Label printer (Zebra ZD420, DYMO 450, Brother QL-820NWB)
- **Network**: Stable internet connection

## Troubleshooting

### Scanner Not Working
1. Check USB connection
2. Verify scanner is in keyboard wedge mode
3. Test scanner with notepad
4. Check scanner settings in app

### Printer Not Responding
1. Verify printer is powered on
2. Check USB/network connection
3. Ensure correct printer driver installed
4. Test print from settings page

### Ads Not Displaying
1. Check file format is supported
2. Verify file size is under 50MB
3. Clear browser cache
4. Check browser console for errors

## Support

For issues, questions, or contributions:
- Create an issue in the repository
- Contact system administrator
- Check documentation

## License

Copyright © 2025 Kiosk Management System. All rights reserved.

## Version History

- **v1.0.0** (2025-09-30)
  - Initial release
  - Home page with statistics
  - Admin dashboard
  - Settings page
  - Ads management
  - Horizontal and vertical kiosk views
  - QR code scanning
  - Badge printing integration
  - Real-time monitoring

