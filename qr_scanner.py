import serial
import time
import requests
import json
import os
import threading
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from dotenv import load_dotenv

CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'config.json')

load_dotenv()

def load_config():
    try:
        with open(CONFIG_PATH, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        print("⚠️ Config file not found or invalid.")
        return {}


class ConfigWatcher(FileSystemEventHandler):
    def __init__(self, on_config_change):
        super().__init__()
        self.on_config_change = on_config_change

    def on_modified(self, event):
        if os.path.abspath(event.src_path) == os.path.abspath(CONFIG_PATH):
            print("🔄 Config file changed. Reloading...")
            self.on_config_change()


class ScannerApp:
    def __init__(self):
        self.config = load_config()
        self.ser = None
        self.lock = threading.Lock()
        self.load_serial()

    def load_serial(self):
        with self.lock:
            try:
                if self.ser and self.ser.is_open:
                    self.ser.close()
                port = self.config.get("scanner_com_port")
                baud = self.config.get("baud_rate", 9600)
                if port:
                    self.ser = serial.Serial(port, baud, timeout=1)
                    print(f"✅ Serial port {port} opened")
                else:
                    print("⚠️ scanner_com_port not set in config.")
                    self.ser = None
            except Exception as e:
                print(f"❌ Failed to open serial port: {e}")
                self.ser = None

    def on_config_change(self):
        new_config = load_config()
        if new_config != self.config:
            print("🔁 Applying new configuration...")
            self.config = new_config
            self.load_serial()

    def run(self):
        in_vcard = False
        vcard_lines = []

        while True:
            try:
                if not self.ser or not self.ser.is_open:
                    time.sleep(1)
                    continue

                line = self.ser.readline().decode('utf-8', errors='ignore').strip()
                if not line:
                    continue

                if line == "BEGIN:VCARD":
                    in_vcard = True
                    vcard_lines = [line]

                elif line == "END:VCARD" and in_vcard:
                    vcard_lines.append(line)
                    json_data = vcard_to_json(vcard_lines)
                    test_api(json_data, self.config)
                    in_vcard = False
                    vcard_lines = []

                elif in_vcard:
                    vcard_lines.append(line)

            except Exception as e:
                print(f"❌ Error in reading loop: {e}")
                in_vcard = False
                vcard_lines = []
                time.sleep(1)


def vcard_to_json(lines):
    data = {}
    for line in lines:
        if ':' in line:
            key, value = line.split(':', 1)
            if key not in ["BEGIN", "END"]:
                data[key.lower()] = value.strip()
    return data


def test_api(vcard_data, config):
    api_url = config.get("api_url")
    api_key = os.getenv("API_KEY")
    fair_code = config.get("fair_code")

    if not all([api_url, api_key, fair_code]):
        print("⚠️ Missing API configuration.")
        return

    headers = {
        'x-api-key': api_key,
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    payload = {
        'vbarcode': json.dumps(vcard_data),
        'faircode': fair_code
    }

    try:
        response = requests.post(api_url, headers=headers, data=payload, timeout=10)

        if response.status_code == 200:
            print("✅ API response:", response.json())
        else:
            print(f"❌ API error: {response.status_code} - {response.text}")

    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")

def process_vcard(vcard_data, config):
    api_url = config.get("api_url")
    api_key = os.getenv("API_KEY")
    fair_code = config.get("fair_code")

    if not all([api_url, api_key, fair_code]):
        print("⚠️ Missing API configuration.")
        return

    headers = {
        'x-api-key': api_key,
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    payload = {
        'vbarcode': json.dumps(vcard_data),
        'faircode': fair_code
    }

    try:
        response = requests.post(api_url, headers=headers, data=payload, timeout=10)

        if response.status_code == 200:
            print("✅ API response:", response.json())
        else:
            print(f"❌ API error: {response.status_code} - {response.text}")

    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")

if __name__ == "__main__":
    app = ScannerApp()

    # Set up watchdog observer
    event_handler = ConfigWatcher(app.on_config_change)
    observer = Observer()
    observer.schedule(event_handler, path=os.path.dirname(CONFIG_PATH), recursive=False)
    observer.start()

    try:
        app.run()
    except KeyboardInterrupt:
        print("🛑 Shutting down...")
    finally:
        observer.stop()
        observer.join()
