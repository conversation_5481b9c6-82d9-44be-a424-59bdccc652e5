{% extends "base.html" %}

{% block title %}Scanner Status - Kiosk Management System{% endblock %}

{% block page_header %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          <i class="ti ti-qrcode icon me-2"></i>
          Scanner Status
        </h2>
        <div class="text-muted mt-1">Real-time QR scanner monitoring and status</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <button class="btn btn-outline-secondary" onclick="simulateScan()">
            <i class="ti ti-test-pipe icon"></i>
            Simulate Scan
          </button>
          <button class="btn btn-outline-primary" onclick="testScanner()">
            <i class="ti ti-refresh icon"></i>
            Test Scanner
          </button>
          <button class="btn btn-outline-warning" onclick="restartScanner()">
            <i class="ti ti-reload icon"></i>
            Restart
          </button>
          <a href="{{ url_for('settings') }}" class="btn btn-primary">
            <i class="ti ti-settings icon"></i>
            Configure
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block content %}
<div class="row row-deck row-cards">
  <!-- Scanner Status Card -->
  <div class="col-lg-4">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Scanner Status</h3>
      </div>
      <div class="card-body text-center">
        <div class="scanner-status-indicator mb-3">
          <div id="status-icon" class="status-icon status-disconnected">
            <i class="ti ti-qrcode icon-lg"></i>
          </div>
        </div>
        <h3 id="status-text" class="text-muted">Checking...</h3>
        <p id="status-description" class="text-muted small">Initializing scanner connection</p>
        <div class="mt-3">
          <span class="badge badge-outline" id="status-badge">Unknown</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Scanner Configuration -->
  <div class="col-lg-4">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Configuration</h3>
      </div>
      <div class="card-body">
        <div class="row mb-2">
          <div class="col-5 text-muted">COM Port:</div>
          <div class="col-7"><strong>{{ kiosk.scanner_com_port or 'Not Set' }}</strong></div>
        </div>
        <div class="row mb-2">
          <div class="col-5 text-muted">Baud Rate:</div>
          <div class="col-7"><strong>{{ kiosk.baud_rate or '9600' }}</strong></div>
        </div>
        <div class="row mb-2">
          <div class="col-5 text-muted">Kiosk ID:</div>
          <div class="col-7"><strong>{{ kiosk.kiosk_id }}</strong></div>
        </div>
        <div class="row mb-2">
          <div class="col-5 text-muted">API URL:</div>
          <div class="col-7"><small>{{ kiosk.api_url or 'Not Set' }}</small></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Last Scan Info -->
  <div class="col-lg-4">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Last Scan</h3>
      </div>
      <div class="card-body">
        <div id="last-scan-info">
          <div class="text-center text-muted">
            <i class="ti ti-scan icon-lg mb-2"></i>
            <p>No scans yet</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Scans -->
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Recent Scans</h3>
        <div class="card-actions">
          <button class="btn btn-sm btn-outline-primary" onclick="clearScans()">
            <i class="ti ti-trash icon"></i>
            Clear
          </button>
        </div>
      </div>
      <div class="card-body">
        <div id="recent-scans">
          <div class="text-center text-muted py-4">
            <i class="ti ti-history icon-lg mb-2"></i>
            <p>No recent scans to display</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scanner Activity Log -->
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Activity Log</h3>
        <div class="card-actions">
          <button class="btn btn-sm btn-outline-primary" onclick="clearLog()">
            <i class="ti ti-trash icon"></i>
            Clear Log
          </button>
        </div>
      </div>
      <div class="card-body">
        <div id="activity-log" class="activity-log">
          <!-- Activity log entries will be populated by JavaScript -->
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.scanner-status-indicator {
  position: relative;
  display: inline-block;
}

.status-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transition: all 0.3s ease;
  position: relative;
}

.status-icon i {
  font-size: 2.5rem;
  color: white;
}

.status-ready {
  background: linear-gradient(135deg, #28a745, #20c997);
  box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
  animation: pulse-ready 2s infinite;
}

.status-connected {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
  box-shadow: 0 0 20px rgba(23, 162, 184, 0.3);
}

.status-scanning {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
  animation: pulse-scanning 1s infinite;
}

.status-disconnected {
  background: linear-gradient(135deg, #6c757d, #495057);
  box-shadow: 0 0 20px rgba(108, 117, 125, 0.3);
}

.status-error {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
  box-shadow: 0 0 20px rgba(220, 53, 69, 0.3);
  animation: pulse-error 1.5s infinite;
}

@keyframes pulse-ready {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes pulse-scanning {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(5deg); }
}

@keyframes pulse-error {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.03); }
}

.activity-log {
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.375rem;
}

.log-entry {
  display: block;
  margin-bottom: 0.25rem;
  padding: 0.25rem 0;
  border-bottom: 1px solid #e9ecef;
}

.log-time {
  color: #6c757d;
  margin-right: 1rem;
}

.log-message {
  color: #495057;
}

.scan-item {
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
  background: white;
  transition: all 0.2s ease;
}

.scan-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.1);
}

.scan-time {
  font-size: 0.875rem;
  color: #6c757d;
}

.scan-data {
  font-weight: 500;
  color: #495057;
}

.badge-outline {
  border: 1px solid currentColor;
  background: transparent;
}

/* Enhanced animations and effects */
.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.scan-item {
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.log-entry {
  animation: fadeInDown 0.2s ease;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Status indicator enhancements */
.status-icon::before {
  content: '';
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  opacity: 0;
  transition: all 0.3s ease;
}

.status-ready::before {
  background: #28a745;
  opacity: 0.3;
  animation: ping 2s infinite;
}

.status-scanning::before {
  background: #ffc107;
  opacity: 0.5;
  animation: ping 1s infinite;
}

.status-error::before {
  background: #dc3545;
  opacity: 0.4;
  animation: ping 1.5s infinite;
}

@keyframes ping {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.2;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Button enhancements */
.btn {
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn:active {
  transform: translateY(0);
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .status-icon {
    width: 60px;
    height: 60px;
  }

  .status-icon i {
    font-size: 2rem;
  }

  .activity-log {
    max-height: 200px;
    font-size: 0.8rem;
  }

  .scan-item {
    padding: 0.5rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .activity-log {
    background: #2d3748;
    color: #e2e8f0;
  }

  .log-entry {
    border-bottom-color: #4a5568;
  }

  .log-time {
    color: #a0aec0;
  }

  .log-message {
    color: #e2e8f0;
  }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let scanHistory = [];
let activityLog = [];

// Status polling
function updateScannerStatus() {
  fetch('/api/scanner/status')
    .then(response => response.json())
    .then(data => {
      updateStatusDisplay(data.status);
      addLogEntry(`Scanner status: ${data.status}`);
    })
    .catch(error => {
      console.error('Error fetching scanner status:', error);
      updateStatusDisplay('Error');
      addLogEntry('Error fetching scanner status');
    });
}

// Update status display
function updateStatusDisplay(status) {
  const statusIcon = document.getElementById('status-icon');
  const statusText = document.getElementById('status-text');
  const statusDescription = document.getElementById('status-description');
  const statusBadge = document.getElementById('status-badge');

  // Remove all status classes
  statusIcon.className = 'status-icon';
  
  switch(status) {
    case 'Ready':
      statusIcon.classList.add('status-ready');
      statusText.textContent = 'Ready to Scan';
      statusDescription.textContent = 'Scanner is connected and ready for QR codes';
      statusBadge.textContent = 'Ready';
      statusBadge.className = 'badge bg-success';
      break;
    case 'Connected':
      statusIcon.classList.add('status-connected');
      statusText.textContent = 'Connected';
      statusDescription.textContent = 'Scanner is connected to COM port';
      statusBadge.textContent = 'Connected';
      statusBadge.className = 'badge bg-info';
      break;
    case 'Scanning':
      statusIcon.classList.add('status-scanning');
      statusText.textContent = 'Scanning...';
      statusDescription.textContent = 'Processing QR code scan';
      statusBadge.textContent = 'Scanning';
      statusBadge.className = 'badge bg-warning';
      break;
    case 'Disconnected':
      statusIcon.classList.add('status-disconnected');
      statusText.textContent = 'Disconnected';
      statusDescription.textContent = 'Scanner is not connected';
      statusBadge.textContent = 'Disconnected';
      statusBadge.className = 'badge bg-secondary';
      break;
    case 'Not Configured':
      statusIcon.classList.add('status-disconnected');
      statusText.textContent = 'Not Configured';
      statusDescription.textContent = 'Scanner COM port not set in configuration';
      statusBadge.textContent = 'Not Configured';
      statusBadge.className = 'badge bg-warning';
      break;
    case 'Error':
    default:
      statusIcon.classList.add('status-error');
      statusText.textContent = 'Error';
      statusDescription.textContent = 'Scanner connection error';
      statusBadge.textContent = 'Error';
      statusBadge.className = 'badge bg-danger';
      break;
  }
}

// Poll for new scans
function pollForScans() {
  fetch('/api/barcode')
    .then(response => response.json())
    .then(data => {
      if (data.barcode) {
        addScanToHistory(data.barcode, data.scan_time);
        updateLastScanInfo(data.barcode, data.scan_time);
        addLogEntry(`New scan received: ${JSON.stringify(data.barcode)}`);
      }
    })
    .catch(error => {
      console.error('Error polling for scans:', error);
    });
}

// Add scan to history
function addScanToHistory(scanData, scanTime) {
  const scan = {
    data: scanData,
    time: scanTime || new Date().toISOString(),
    id: Date.now()
  };
  
  scanHistory.unshift(scan);
  if (scanHistory.length > 10) {
    scanHistory = scanHistory.slice(0, 10);
  }
  
  updateRecentScansDisplay();
}

// Update recent scans display
function updateRecentScansDisplay() {
  const container = document.getElementById('recent-scans');
  
  if (scanHistory.length === 0) {
    container.innerHTML = `
      <div class="text-center text-muted py-4">
        <i class="ti ti-history icon-lg mb-2"></i>
        <p>No recent scans to display</p>
      </div>
    `;
    return;
  }
  
  container.innerHTML = scanHistory.map(scan => `
    <div class="scan-item">
      <div class="d-flex justify-content-between align-items-start">
        <div class="flex-grow-1">
          <div class="scan-data">${formatScanData(scan.data)}</div>
          <div class="scan-time">${formatTime(scan.time)}</div>
        </div>
        <button class="btn btn-sm btn-outline-danger" onclick="removeScan(${scan.id})">
          <i class="ti ti-x"></i>
        </button>
      </div>
    </div>
  `).join('');
}

// Update last scan info
function updateLastScanInfo(scanData, scanTime) {
  const container = document.getElementById('last-scan-info');
  container.innerHTML = `
    <div class="scan-data mb-2">${formatScanData(scanData)}</div>
    <div class="scan-time text-muted">${formatTime(scanTime)}</div>
  `;
}

// Format scan data for display
function formatScanData(data) {
  if (typeof data === 'string') {
    return data;
  }
  
  if (data.email) {
    let display = `<strong>${data.email}</strong>`;
    if (data.name) display += `<br><small>${data.name}</small>`;
    if (data.company) display += `<br><small>${data.company}</small>`;
    return display;
  }
  
  return JSON.stringify(data);
}

// Format time for display
function formatTime(timeString) {
  if (!timeString) return 'Unknown time';
  const date = new Date(timeString);
  return date.toLocaleString();
}

// Add log entry
function addLogEntry(message) {
  const entry = {
    time: new Date().toLocaleTimeString(),
    message: message,
    id: Date.now()
  };
  
  activityLog.unshift(entry);
  if (activityLog.length > 50) {
    activityLog = activityLog.slice(0, 50);
  }
  
  updateActivityLogDisplay();
}

// Update activity log display
function updateActivityLogDisplay() {
  const container = document.getElementById('activity-log');
  container.innerHTML = activityLog.map(entry => `
    <div class="log-entry">
      <span class="log-time">${entry.time}</span>
      <span class="log-message">${entry.message}</span>
    </div>
  `).join('');
  
  // Auto-scroll to top
  container.scrollTop = 0;
}

// Test scanner function
function testScanner() {
  addLogEntry('Testing scanner connection...');

  fetch('/api/scanner/test', { method: 'POST' })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        addLogEntry(`Scanner test successful: ${data.message}`);
        updateScannerStatus();
      } else {
        addLogEntry(`Scanner test failed: ${data.message}`);
      }
    })
    .catch(error => {
      addLogEntry(`Scanner test error: ${error.message}`);
    });
}

// Restart scanner function
function restartScanner() {
  addLogEntry('Restarting scanner...');

  fetch('/api/scanner/restart', { method: 'POST' })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        addLogEntry(`Scanner restart successful: ${data.message}`);
        updateScannerStatus();
      } else {
        addLogEntry(`Scanner restart failed: ${data.message}`);
      }
    })
    .catch(error => {
      addLogEntry(`Scanner restart error: ${error.message}`);
    });
}

// Simulate scan function
function simulateScan() {
  const email = prompt('Enter email address to simulate scan:', '<EMAIL>');
  if (!email) return;

  addLogEntry(`Simulating scan for: ${email}`);

  fetch('/api/scanner/simulate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      qr_data: {
        email: email,
        name: 'Test User',
        company: 'Test Company'
      }
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      addLogEntry(`Scan simulation successful: ${data.message}`);
      // The scan will be picked up by the polling function
    } else {
      addLogEntry(`Scan simulation failed: ${data.message || 'Unknown error'}`);
    }
  })
  .catch(error => {
    addLogEntry(`Scan simulation error: ${error.message}`);
  });
}

// Clear functions
function clearScans() {
  scanHistory = [];
  updateRecentScansDisplay();
  document.getElementById('last-scan-info').innerHTML = `
    <div class="text-center text-muted">
      <i class="ti ti-scan icon-lg mb-2"></i>
      <p>No scans yet</p>
    </div>
  `;
  addLogEntry('Scan history cleared');
}

function clearLog() {
  activityLog = [];
  updateActivityLogDisplay();
  addLogEntry('Activity log cleared');
}

function removeScan(scanId) {
  scanHistory = scanHistory.filter(scan => scan.id !== scanId);
  updateRecentScansDisplay();
  addLogEntry('Scan removed from history');
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
  // Initialize activity log
  activityLog = [];
  addLogEntry('Scanner status page initialized');
  updateScannerStatus();

  // Set up polling intervals
  setInterval(updateScannerStatus, 5000); // Check status every 5 seconds
  setInterval(pollForScans, 1000); // Check for new scans every second
});
</script>
{% endblock %}
