from flask import Flask, render_template, request, redirect, url_for, jsonify, flash, Response
from datetime import datetime
from dotenv import load_dotenv
import requests
import os
import serial
import threading
import json
import time
import queue
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

load_dotenv()

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-in-production'

#load config
def load_config():
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Config file not found. Using default config.")
        return {
            # "scanner_com_port": "COM9",
            # "baud_rate": 9600,
            # "api_url": "http://127.0.0.1/api_famev2/v1/process-vcard",
            # "api_key": "31d051aa-7c55-4dad-91f8-e631c0f4af3a",
            # "fair_code": "MFIO2025"
        }

def update_config(new_config):
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'w') as f:
            json.dump(new_config, f, indent=4)
    except Exception as e:
        print(f"Failed to write config file: {e}")

# Configuration
UPLOAD_FOLDER = 'static/uploads/ads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'mp4', 'webm', 'mov'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Global variables for barcode scanning
last_barcode = None
last_scan_time = None
scanner_status = "Disconnected"
scanner_thread = None
lock = threading.Lock()

# Event system for real-time updates
event_queues = []
event_lock = threading.Lock()

def broadcast_event(event_type, data):
    """Broadcast event to all connected clients"""
    with event_lock:
        event = {
            'type': event_type,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
        # Remove closed queues
        global event_queues
        event_queues = [q for q in event_queues if not q.empty() or q.qsize() < 100]

        # Add event to all active queues
        for q in event_queues:
            try:
                q.put_nowait(event)
            except queue.Full:
                pass  # Skip if queue is full

# Scanner configuration watcher
class ConfigWatcher(FileSystemEventHandler):
    def __init__(self, callback):
        self.callback = callback

    def on_modified(self, event):
        if event.src_path.endswith('config.json'):
            self.callback()

# vCard parsing function
def vcard_to_json(vcard_lines):
    """Convert vCard lines to JSON format"""
    data = {}
    for line in vcard_lines:
        if ':' in line:
            key, value = line.split(':', 1)
            if key == 'FN':
                data['name'] = value
            elif key == 'EMAIL':
                data['email'] = value
            elif key == 'ORG':
                data['company'] = value
    data['raw'] = vcard_lines
    return data

# Integrated Scanner Class
class IntegratedScanner:
    def __init__(self):
        self.config = load_config()
        self.ser = None
        self.running = False
        self.lock = threading.Lock()
        self.load_serial()

    def load_serial(self):
        global scanner_status
        with self.lock:
            try:
                if self.ser and self.ser.is_open:
                    self.ser.close()

                port = self.config.get("scanner_com_port")
                baud = self.config.get("baud_rate", 9600)

                if port:
                    self.ser = serial.Serial(port, baud, timeout=1)
                    scanner_status = "Connected"
                    print(f"✅ Serial port {port} opened successfully")
                    broadcast_event('scanner_status', {'status': scanner_status, 'port': port})
                else:
                    scanner_status = "Not Configured"
                    print("⚠️ scanner_com_port not set in config.")
                    self.ser = None
                    broadcast_event('scanner_status', {'status': scanner_status, 'message': 'COM port not configured'})
            except Exception as e:
                scanner_status = "Error"
                print(f"❌ Failed to open serial port: {e}")
                self.ser = None
                broadcast_event('scanner_status', {'status': scanner_status, 'error': str(e)})

    def on_config_change(self):
        new_config = load_config()
        if new_config != self.config:
            print("🔁 Applying new scanner configuration...")
            self.config = new_config
            self.load_serial()

    def run(self):
        global last_barcode, last_scan_time, scanner_status
        self.running = True
        in_vcard = False
        vcard_lines = []

        while self.running:
            try:
                if not self.ser or not self.ser.is_open:
                    scanner_status = "Disconnected"
                    time.sleep(1)
                    continue

                scanner_status = "Ready"
                line = self.ser.readline().decode('utf-8', errors='ignore').strip()

                if not line:
                    continue

                print(f"Scanner received: {line}")

                # Handle vCard format
                if line == "BEGIN:VCARD":
                    in_vcard = True
                    vcard_lines = [line]
                    scanner_status = "Scanning"
                    broadcast_event('scanner_status', {'status': scanner_status})

                elif line == "END:VCARD" and in_vcard:
                    vcard_lines.append(line)
                    # json_data = vcard_to_json(vcard_lines)
                    json_data = vcard_lines

                    with lock:
                        last_barcode = json_data
                        last_scan_time = datetime.now()

                    # Broadcast scan event
                    broadcast_event('new_scan', {
                        'barcode': vcard_to_json(vcard_lines),
                        # 'barcode': json_data,
                        'scan_time': last_scan_time.isoformat()
                    })

                    # Process the vCard data
                    self.process_vcard(json_data)
                    in_vcard = False
                    vcard_lines = []
                    scanner_status = "Ready"
                    broadcast_event('scanner_status', {'status': scanner_status})

                elif in_vcard:
                    vcard_lines.append(line)

                # Handle simple text/email format
                elif not in_vcard and line:
                    with lock:
                        last_barcode = {"email": line, "raw": line}
                        last_scan_time = datetime.now()

                    # Broadcast scan event
                    broadcast_event('new_scan', {
                        'barcode': {"email": line, "raw": line},
                        'scan_time': last_scan_time.isoformat()
                    })

                    scanner_status = "Ready"
                    broadcast_event('scanner_status', {'status': scanner_status})

            except Exception as e:
                print(f"❌ Error in scanner loop: {e}")
                scanner_status = "Error"
                in_vcard = False
                vcard_lines = []
                time.sleep(1)

    def process_vcard(self, vcard_data):
        """Process vCard data and send to API"""
        config = load_config()
        api_url = config.get("api_url")
        fair_code = config.get("fair_code")

        if not api_url:
            print("⚠️ API URL not configured")
            return

        headers = {
            'x-api-key': os.getenv("API_KEY"),
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        payload = {
            'vbarcode': json.dumps(vcard_data),
            'faircode': fair_code
        }

        try:
            response = requests.post(api_url, headers=headers, data=payload, timeout=10)
            if response.status_code == 200:
                print("✅ API response:", response.json())
            else:
                print(f"❌ API error: {response.status_code} - {response.text}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")

    def stop(self):
        self.running = False
        if self.ser and self.ser.is_open:
            self.ser.close()

# Initialize scanner instance
scanner_instance = IntegratedScanner()

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Routes
@app.route('/')
def index():
    config = load_config()
    """Home page / Landing page"""
    return render_template('index.html',kiosk=config)

@app.route('/dashboard')
def dashboard():
    """Admin dashboard with statistics"""
    config = load_config()
    # Mock data - replace with actual database queries
    stats = {
        'total_scans': 1247,
        'today_scans': 89,
        'total_badges': 1198,
        'active_ads': 12,
        'printer_status': 'Online',
        'scanner_status': 'Online'
    }
    recent_scans = [
        {'id': 1, 'name': 'John Doe', 'company': 'Tech Corp', 'time': '10:45 AM', 'status': 'Printed'},
        {'id': 2, 'name': 'Jane Smith', 'company': 'Innovation Ltd', 'time': '10:42 AM', 'status': 'Printed'},
        {'id': 3, 'name': 'Bob Johnson', 'company': 'StartUp Inc', 'time': '10:38 AM', 'status': 'Printed'},
        {'id': 4, 'name': 'Alice Williams', 'company': 'Global Solutions', 'time': '10:35 AM', 'status': 'Printed'},
        {'id': 5, 'name': 'Charlie Brown', 'company': 'Enterprise Co', 'time': '10:30 AM', 'status': 'Printed'},
    ]
    return render_template('dashboard.html', stats=stats, recent_scans=recent_scans,kiosk=config)

# @app.route('/settings')
# def settings():
#     """Settings and configuration page"""
#     return render_template('settings.html')

@app.route('/settings')
def settings():
    """Settings and configuration page"""
    config = load_config()
    return render_template('settings.html', kiosk=config)

@app.route('/scanner-status')
def scanner_status_page():
    """Scanner status monitoring page"""
    config = load_config()
    return render_template('scanner_status.html', kiosk=config)

@app.route('/update_settings', methods=['POST'])
def update_settings():
    """Update kiosk settings"""
    scanner_com_port = request.form.get('scanner_com_port')
    # 1. Validate input exists
    if not scanner_com_port:
        return jsonify({'error': 'Missing scanner_com_port'}), 400

    # 2. Load existing config
    config = load_config()
    
    # 3. Update the config
    config['scanner_com_port'] = scanner_com_port

    # 4. Save back to the file
    update_config(config)

    return jsonify({'message': 'Settings updated successfully'}), 200

@app.route('/update_general_settings', methods=['POST'], endpoint='update_general_settings')
def update_general_settings():
    """Update kiosk settings"""
    kiosk_id = request.form.get('kiosk_id')
    event_name = request.form.get('event_name')
    event_location = request.form.get('event_location')
    event_start_date = request.form.get('event_start_date')
    event_end_date = request.form.get('event_end_date')
    welcome_message = request.form.get('welcome_message')
    # 1. Validate input exists
    if not event_name:
        return jsonify({'error': 'Missing event_name'}), 400
    if not event_location:
        return jsonify({'error': 'Missing event_location'}), 400
    if not event_start_date:
        return jsonify({'error': 'Missing event_start_date'}), 400
    if not event_end_date:
        return jsonify({'error': 'Missing event_end_date'}), 400
    if not welcome_message:
        return jsonify({'error': 'Missing welcome_message'}), 400
    if not kiosk_id:
        return jsonify({'error': 'Missing kiosk_id'}), 400
    

    # 2. Load existing config
    config = load_config()
    
    # 3. Update the config
    config['event_name'] = event_name
    config['event_location'] = event_location
    config['event_start_date'] = event_start_date
    config['event_end_date'] = event_end_date
    config['welcome_message'] = welcome_message
    config['kiosk_id'] = kiosk_id

    # 4. Save back to the file
    update_config(config)

    return jsonify({'message': 'Settings updated successfully'}), 200

@app.route('/update_api_settings', methods=['POST'])
def update_api_settings():
    """Update kiosk settings"""
    api_url = request.form.get('api_url')
    # 1. Validate input exists
    if not api_url:
        return jsonify({'error': 'Missing api_url'}), 400

    # 2. Load existing config
    config = load_config()
    
    # 3. Update the config
    config['api_url'] = api_url

    # 4. Save back to the file
    update_config(config)

    return jsonify({'message': 'Settings updated successfully'}), 200

@app.route('/kiosk/horizontal')
def kiosk_horizontal():
    """Kiosk view for horizontal/landscape monitors"""
    config = load_config()
    expo_info = {
        'name': config['event_name'],
        'date': config['event_start_date'] + ' - ' + config['event_end_date'],
        'location': config['event_location'],
        # 'booth_count': 250
    }
    return render_template('kiosk_horizontal.html', expo_info=expo_info)

@app.route('/kiosk/vertical')
def kiosk_vertical():
    """Kiosk view for vertical/portrait monitors"""
    config = load_config()
    expo_info = {
        'name': config['event_name'],
        'date': config['event_start_date'] + ' - ' + config['event_end_date'],
        'location': config['event_location'],
        'booth_count':''
    }
    return render_template('kiosk_vertical.html', kiosk=config,expo_info=expo_info)

@app.route('/ads')
def ads_management():
    """Ads management page"""
    config = load_config()
    # Get list of uploaded ads
    ads_list = []
    if os.path.exists(UPLOAD_FOLDER):
        for filename in os.listdir(UPLOAD_FOLDER):
            if allowed_file(filename):
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                file_type = 'video' if filename.rsplit('.', 1)[1].lower() in ['mp4', 'webm', 'mov'] else 'image'
                ads_list.append({
                    'filename': filename,
                    'type': file_type,
                    'size': os.path.getsize(file_path),
                    'url': url_for('static', filename=f'uploads/ads/{filename}')
                })
    return render_template('ads_management.html', kiosk=config, ads=ads_list)

@app.route('/ads/upload', methods=['POST'])
def upload_ad():
    """Handle ad file upload"""
    config = load_config()
    if 'file' not in request.files:
        flash('No file selected', 'danger')
        return redirect(url_for('ads_management'))

    file = request.files['file']
    if file.filename == '':
        flash('No file selected', 'danger')
        return redirect(url_for('ads_management'))

    if file and allowed_file(file.filename):
        filename = file.filename
        file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        flash(f'File {filename} uploaded successfully', 'success')
    else:
        flash('Invalid file type', 'danger')

    return redirect(url_for('ads_management'))

@app.route('/ads/delete/<filename>', methods=['POST'])
def delete_ad(filename):
    """Delete an ad file"""
    config = load_config()
    try:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            flash(f'File {filename} deleted successfully', 'success')
        else:
            flash('File not found', 'danger')
    except Exception as e:
        flash(f'Error deleting file: {str(e)}', 'danger')

    return redirect(url_for('ads_management'))

@app.route('/upload/header', methods=['POST'])
def upload_header():
    """Upload header image for kiosk"""
    config = load_config()
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'No file selected'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'No file selected'}), 400

    if file and allowed_file(file.filename):
        # Save as header.jpg (overwrite existing)
        file_path = os.path.join('static/uploads', 'header.jpg')
        file.save(file_path)
        return jsonify({'success': True, 'message': 'Header image uploaded successfully'})

    return jsonify({'success': False, 'message': 'Invalid file type'}), 400

@app.route('/upload/schedule', methods=['POST'])
def upload_schedule():
    """Upload schedule image for kiosk"""
    config = load_config()
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'No file selected'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'No file selected'}), 400

    if file and allowed_file(file.filename):
        # Save as schedule.jpg (overwrite existing)
        file_path = os.path.join('static/uploads', 'schedule.jpg')
        file.save(file_path)
        return jsonify({'success': True, 'message': 'Schedule image uploaded successfully'})

    return jsonify({'success': False, 'message': 'Invalid file type'}), 400

# API endpoints for kiosk
@app.route('/api/scan', methods=['POST'])
def api_scan():
    """API endpoint to process QR code scan"""
    config = load_config()
    data = request.get_json()
    qr_code = data.get('qr_code', '')

    # Mock response - replace with actual QR processing and database lookup
    response = {
        'success': True,
        'participant': {
            'name': 'John Doe',
            'company': 'Tech Corporation',
            'position': 'CEO',
            'email': '<EMAIL>',
            'badge_type': 'VIP'
        },
        'print_status': 'queued'
    }

    return jsonify(response)

@app.route('/api/ads')
def api_ads():
    """API endpoint to get list of ads for kiosk display"""
    config = load_config()
    ads_list = []
    if os.path.exists(UPLOAD_FOLDER):
        for filename in os.listdir(UPLOAD_FOLDER):
            if allowed_file(filename):
                file_type = 'video' if filename.rsplit('.', 1)[1].lower() in ['mp4', 'webm', 'mov'] else 'image'
                ads_list.append({
                    'filename': filename,
                    'type': file_type,
                    'url': url_for('static', filename=f'uploads/ads/{filename}')
                })
    return jsonify(ads_list)

@app.route('/api/stats')
def api_stats():
    """API endpoint to get current statistics"""
    global scanner_status
    stats = {
        'total_scans': 1247,
        'today_scans': 89,
        'printer_status': 'Online',
        'scanner_status': scanner_status,
        'timestamp': datetime.now().isoformat()
    }
    return jsonify(stats)

@app.route('/api/barcode')
def api_barcode():
    """API endpoint to get the last scanned barcode"""
    global last_barcode, last_scan_time
    with lock:
        barcode = last_barcode
        scan_time = last_scan_time.isoformat() if last_scan_time else None
        last_barcode = None  # Reset after reading
        return jsonify(barcode=barcode, scan_time=scan_time)

@app.route('/api/scanner/status')
def api_scanner_status():
    """API endpoint to get scanner status"""
    global scanner_status, last_scan_time
    return jsonify({
        'status': scanner_status,
        'last_scan_time': last_scan_time.isoformat() if last_scan_time else None,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/scanner/test', methods=['POST'])
def api_scanner_test():
    """API endpoint to test scanner connection"""
    try:
        scanner_instance.load_serial()
        return jsonify({
            'success': True,
            'status': scanner_status,
            'message': 'Scanner test completed'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'status': 'Error',
            'message': str(e)
        }), 500

@app.route('/api/scanner/restart', methods=['POST'])
def api_scanner_restart():
    """API endpoint to restart scanner connection"""
    global scanner_thread
    try:
        # Stop current scanner
        scanner_instance.stop()
        if scanner_thread and scanner_thread.is_alive():
            scanner_thread.join(timeout=2)

        # Restart scanner
        start_scanner()

        return jsonify({
            'success': True,
            'message': 'Scanner restarted successfully',
            'status': scanner_status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to restart scanner: {str(e)}',
            'status': 'Error'
        }), 500

@app.route('/api/scanner/config')
def api_scanner_config():
    """API endpoint to get scanner configuration"""
    config = load_config()
    return jsonify({
        'com_port': config.get('scanner_com_port'),
        'baud_rate': config.get('baud_rate', 9600),
        'status': scanner_status,
        'thread_alive': scanner_thread.is_alive() if scanner_thread else False
    })

@app.route('/api/scanner/simulate', methods=['POST'])
def api_scanner_simulate():
    """API endpoint to simulate a QR scan for testing"""
    global last_barcode, last_scan_time
    data = request.get_json()

    if not data or 'qr_data' not in data:
        return jsonify({'error': 'No QR data provided'}), 400

    qr_data = data['qr_data']

    # Simulate scan
    with lock:
        if isinstance(qr_data, str):
            last_barcode = {"email": qr_data, "raw": qr_data, "simulated": True}
        else:
            last_barcode = {**qr_data, "simulated": True}
        last_scan_time = datetime.now()

    # Broadcast simulated scan event
    broadcast_event('new_scan', {
        'barcode': last_barcode,
        'scan_time': last_scan_time.isoformat()
    })

    return jsonify({
        'success': True,
        'message': 'QR scan simulated successfully',
        'data': last_barcode
    })

@app.route('/api/events')
def api_events():
    """Server-Sent Events endpoint for real-time updates"""
    def event_stream():
        # Create a new queue for this client
        client_queue = queue.Queue(maxsize=50)

        with event_lock:
            event_queues.append(client_queue)

        try:
            # Send initial status
            yield f"data: {json.dumps({'type': 'scanner_status', 'data': {'status': scanner_status}, 'timestamp': datetime.now().isoformat()})}\n\n"

            while True:
                try:
                    # Wait for new events (with timeout to send heartbeat)
                    event = client_queue.get(timeout=30)
                    yield f"data: {json.dumps(event)}\n\n"
                except queue.Empty:
                    # Send heartbeat to keep connection alive
                    yield f"data: {json.dumps({'type': 'heartbeat', 'timestamp': datetime.now().isoformat()})}\n\n"
                except:
                    break
        finally:
            # Remove queue when client disconnects
            with event_lock:
                if client_queue in event_queues:
                    event_queues.remove(client_queue)

    return Response(event_stream(), mimetype='text/event-stream',
                   headers={'Cache-Control': 'no-cache',
                           'Connection': 'keep-alive',
                           'Access-Control-Allow-Origin': '*'})

@app.route('/api/process-vcard', methods=['POST'])
def api_process_vcard():
    """API endpoint to process vCard QR code and send to external API"""
    config = load_config()
    data = request.get_json()
    vcard_data = data.get('vcard', '')

    if not vcard_data:
        return jsonify({'error': 'No vCard data provided'}), 400

    # API configuration - Update this URL to your actual API endpoint
    api_url = config.get("api_url")

    headers = {
        'x-api-key': os.getenv("API_KEY"),
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    # Prepare payload with vbarcode and faircode
    payload = {
        'vbarcode': vcard_data,
        'faircode': config.get("fair_code")
    }

    try:
        # Send POST request to external API
        response = requests.post(api_url, json=payload, headers=headers, timeout=10)

        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({
                'error': f'API error: {response.status_code}',
                'details': response.text
            }), response.status_code

    except requests.exceptions.RequestException as e:
        return jsonify({'error': str(e)}), 500

def start_scanner():
    """Start the integrated scanner in a background thread"""
    global scanner_thread
    if scanner_thread is None or not scanner_thread.is_alive():
        scanner_thread = threading.Thread(target=scanner_instance.run, daemon=True)
        scanner_thread.start()
        print("🚀 Integrated scanner started")

def setup_config_watcher():
    """Setup file watcher for config changes"""
    event_handler = ConfigWatcher(scanner_instance.on_config_change)
    observer = Observer()
    observer.schedule(event_handler, path=os.path.dirname(__file__), recursive=False)
    observer.start()
    return observer

if __name__ == '__main__':
    print("🚀 Starting Kiosk Application...")

    # Start the integrated scanner
    start_scanner()

    # Setup config file watcher
    config_observer = setup_config_watcher()

    try:
        # Start the Flask app
        print("🌐 Starting Flask server on http://0.0.0.0:5000")
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("🛑 Shutting down...")
    finally:
        scanner_instance.stop()
        config_observer.stop()
        config_observer.join()