# QR Scanner & API Integration Guide

## 🎯 Overview

The kiosk system now integrates with a hardware QR scanner and external API to lookup participant information and display it on screen before printing badges.

---

## 🔄 How It Works

### Workflow

```
┌─────────────────────────────────────────────────────────────┐
│ 1. Participant scans QR code (email address)               │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 2. Hardware scanner sends data via serial port (COM port)  │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 3. Flask app reads from serial port (background thread)    │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 4. Kiosk frontend polls /api/barcode every 500ms           │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 5. New barcode detected → Call /api/buyer/<email>          │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 6. Flask app calls external API with x-api-key header      │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 7. Display participant info on screen                      │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────┐
│ 8. Print badge (5 second display)                          │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔌 Hardware Setup

### QR Scanner Configuration

1. **Connect Scanner**:
   - USB QR code scanner
   - Configured as "keyboard wedge" mode OR serial port mode

2. **Serial Port Mode** (Recommended):
   - Connect to COM port (e.g., COM3, COM4)
   - Configure in Settings page
   - Baud rate: 9600 (default)

3. **Scanner Settings**:
   - Output format: Raw data (email address)
   - Suffix: None (or configure in code)
   - Prefix: None

---

## 🔧 Backend Configuration

### 1. Serial Port Setup (app.py)

```python
# Global variables for barcode scanning
last_barcode = None
lock = threading.Lock()

def read_serial():
    """Background thread to read from serial port"""
    global last_barcode
    try:
        kiosk = Kiosk.query.filter_by(kiosk_id=KIOSK_ID).first()
        serial_port = f'COM{kiosk.scanner_com_port}'
        baud_rate = 9600

        with serial.Serial(serial_port, baud_rate, timeout=1) as ser:
            while True:
                line = ser.readline().decode('utf-8').strip()
                if line:
                    with lock:
                        last_barcode = line
                        print(f"Barcode scanned: {last_barcode}")
    except Exception as e:
        print(f"Error: {e}")
```

### 2. API Endpoints

#### `/api/barcode` (GET)
Returns the last scanned barcode.

**Response**:
```json
{
  "barcode": "<EMAIL>"
}
```

#### `/api/buyer/<email>` (GET)
Looks up buyer information from external API.

**Request**:
```
GET /api/buyer/<EMAIL>
```

**External API Call**:
```python
api_url = f'http://127.0.0.1/api_famev2/v1/buyers/email/{email}'
headers = {
    'x-api-key': '31d051aa-7c55-4dad-91f8-e631c0f4af3a',
    'Accept': 'application/json'
}
response = requests.get(api_url, headers=headers, timeout=10)
```

**Response** (Success):
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "company": "ABC Corporation",
  "position": "CEO",
  "phone": "+1234567890",
  "country": "Philippines",
  "badge_type": "VIP"
}
```

**Response** (Error):
```json
{
  "error": "API error: 404"
}
```

---

## 💻 Frontend Integration

### 1. Polling for Barcodes

The kiosk frontend polls the `/api/barcode` endpoint every 500ms:

```javascript
let lastProcessedBarcode = null;

async function pollBarcode() {
  const res = await fetch('/api/barcode');
  const data = await res.json();
  
  if (data.barcode && data.barcode !== lastProcessedBarcode) {
    lastProcessedBarcode = data.barcode;
    await processBarcodeScanned(data.barcode);
  }
}

setInterval(pollBarcode, 500);
```

### 2. Processing Scanned Barcode

```javascript
async function processBarcodeScanned(barcode) {
  // Call buyer lookup API
  const response = await fetch(`/api/buyer/${encodeURIComponent(barcode)}`);
  const data = await response.json();
  
  if (response.ok && !data.error) {
    // Success - display buyer info
    displayBuyerInfo(data);
    showSuccessAnimation();
  } else {
    // Error - show error message
    showErrorAnimation(data.error);
  }
}
```

### 3. Displaying Buyer Information

```javascript
function displayBuyerInfo(data) {
  const fields = [
    { key: 'name', label: 'Name', icon: 'user' },
    { key: 'email', label: 'Email', icon: 'mail' },
    { key: 'company', label: 'Company', icon: 'building' },
    { key: 'position', label: 'Position', icon: 'briefcase' },
    { key: 'phone', label: 'Phone', icon: 'phone' },
    { key: 'country', label: 'Country', icon: 'world' },
    { key: 'badge_type', label: 'Badge Type', icon: 'id-badge' },
  ];
  
  // Build HTML to display each field
  fields.forEach(field => {
    const value = data[field.key];
    if (value) {
      // Display field with icon and value
    }
  });
}
```

---

## 🎨 UI States

### 1. Idle State
- QR icon with pulse animation
- "Welcome! Scan your QR code to print your badge"
- Scanner box with instructions

### 2. Success State (5 seconds)
- Green checkmark icon
- "Participant Found!"
- Buyer information card with:
  - Name
  - Email
  - Company
  - Position
  - Phone
  - Country
  - Badge Type
- "Your badge is printing..."

### 3. Error State (3 seconds)
- Red alert icon
- "Not Found"
- Error message
- Returns to idle state

---

## 🧪 Testing

### Test Without Hardware Scanner

You can test the system by typing an email and pressing Enter:

1. Open kiosk view: http://localhost:5000/kiosk/horizontal
2. Type: `<EMAIL>`
3. Press **Enter**
4. System will call API and display results

### Test With Hardware Scanner

1. Configure COM port in Settings
2. Restart Flask app
3. Scan QR code with email address
4. System automatically processes scan

### Test API Endpoint Directly

```bash
# Test barcode endpoint
curl http://localhost:5000/api/barcode

# Test buyer lookup
curl http://localhost:5000/api/buyer/<EMAIL>
```

---

## ⚙️ Configuration

### 1. Update API Endpoint

Edit `app.py` (line ~298):

```python
api_url = f'http://YOUR_API_SERVER/api_famev2/v1/buyers/email/{email}'
```

### 2. Update API Key

Edit `app.py` (line ~299):

```python
headers = {
    'x-api-key': 'YOUR_API_KEY_HERE',
    'Accept': 'application/json'
}
```

### 3. Configure COM Port

1. Go to Settings page
2. Enter COM port number (e.g., 3 for COM3)
3. Save settings
4. Restart Flask app

### 4. Customize Display Fields

Edit `kiosk_horizontal.html` (line ~750):

```javascript
const fields = [
  { key: 'name', label: 'Name', icon: 'user' },
  { key: 'email', label: 'Email', icon: 'mail' },
  // Add or remove fields as needed
];
```

---

## 🔍 Troubleshooting

### Scanner Not Working

**Problem**: Barcode not detected

**Solutions**:
1. Check COM port configuration in Settings
2. Verify scanner is connected
3. Check serial port permissions
4. Restart Flask app
5. Check console for errors: `print(f"Barcode scanned: {last_barcode}")`

### API Not Responding

**Problem**: "Error connecting to the system"

**Solutions**:
1. Verify API server is running
2. Check API endpoint URL
3. Verify API key is correct
4. Check network connectivity
5. Test API directly with curl

### Participant Not Found

**Problem**: "Participant not found in the system"

**Solutions**:
1. Verify email exists in database
2. Check QR code format (should be email)
3. Test API with known email
4. Check API response format

### Display Issues

**Problem**: Buyer info not showing

**Solutions**:
1. Check browser console for errors
2. Verify API response structure matches expected fields
3. Check `displayBuyerInfo()` function
4. Inspect HTML element `#buyer-details`

---

## 📊 API Response Format

### Expected Response Structure

The system expects the API to return JSON with these fields:

```json
{
  "name": "string",
  "email": "string",
  "company": "string",
  "position": "string",
  "phone": "string",
  "country": "string",
  "badge_type": "string"
}
```

### Alternative Structure

If your API returns nested data:

```json
{
  "data": {
    "name": "string",
    "email": "string",
    ...
  }
}
```

Update the `displayBuyerInfo()` function to access `data.data[field.key]`.

---

## 🚀 Deployment Checklist

- [ ] Configure COM port in Settings
- [ ] Update API endpoint URL
- [ ] Update API key
- [ ] Test scanner hardware
- [ ] Test API connectivity
- [ ] Test with real QR codes
- [ ] Verify badge printing works
- [ ] Test error handling
- [ ] Train staff on system
- [ ] Prepare backup plan

---

## 📝 Notes

- **Polling Interval**: 500ms (adjustable in code)
- **Success Display**: 5 seconds
- **Error Display**: 3 seconds
- **API Timeout**: 10 seconds
- **Baud Rate**: 9600 (standard for most scanners)

---

**Status**: ✅ **Ready for Production**

The QR scanner and API integration is fully functional and ready for deployment!

